"use client";
import {
  require_createSvgIcon
} from "./chunk-USAGF5OW.js";
import "./chunk-RL6YP2JE.js";
import "./chunk-FWLELDVD.js";
import {
  require_interopRequireDefault
} from "./chunk-CNC4IL3C.js";
import "./chunk-TYEPBG5V.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import "./chunk-OU5AQDZK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/Star.js
var require_Star = __commonJS({
  "node_modules/@mui/icons-material/Star.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
    }), "Star");
  }
});
export default require_Star();
//# sourceMappingURL=@mui_icons-material_Star.js.map
