"use client";
import {
  require_createSvgIcon
} from "./chunk-USAGF5OW.js";
import "./chunk-RL6YP2JE.js";
import "./chunk-FWLELDVD.js";
import {
  require_interopRequireDefault
} from "./chunk-CNC4IL3C.js";
import "./chunk-TYEPBG5V.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import "./chunk-OU5AQDZK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/Info.js
var require_Info = __commonJS({
  "node_modules/@mui/icons-material/Info.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-6h2zm0-8h-2V7h2z"
    }), "Info");
  }
});
export default require_Info();
//# sourceMappingURL=@mui_icons-material_Info.js.map
