import { Routes, Route, useLocation, Navigate, Outlet } from 'react-router-dom'
import { useEffect, useState } from 'react'

// Layout components
import Layout from './components/layout/Layout'

// Context Providers
import { TenantProvider } from './contexts/TenantContext'
import { AuthProvider, useAuth } from './contexts/AuthContext'

// Import test utilities
import { runTests } from './utils/testKpiUtils'

// CatchAllRoute component to handle routing based on authentication state
const CatchAllRoute = () => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  } else {
    return <Navigate to="/login" replace />
  }
}

// Common components
import DynamicFavicon from './components/common/DynamicFavicon'
import GlobalLoadingOverlay from './components/common/GlobalLoadingOverlay'

// Auth components
import SessionExpiringDialog from './components/auth/SessionExpiringDialog'

// Data Tools components
import DataToolsDashboard from './components/data-tools/DataToolsDashboard'
import ImportExportDashboard from './components/data-tools/import-export/ImportExportDashboard'
import ValidationDashboard from './components/data-tools/validation/ValidationDashboard'
import BackupDashboard from './components/data-tools/backup/BackupDashboard'
import CreateBackup from './components/data-tools/backup/CreateBackup'
import RestoreBackup from './components/data-tools/backup/RestoreBackup'
import SyncDashboard from './components/data-tools/sync/SyncDashboard'
import PreviewDashboard from './components/data-tools/preview/PreviewDashboard'
import AuditDashboard from './components/data-tools/audit/AuditDashboard'
import UserImport from './components/data-tools/import-export/UserImport'
import ExportUsers from './components/data-tools/import-export/ExportUsers'
import OfficeImport from './components/data-tools/import-export/OfficeImport'
import ExportOffices from './components/data-tools/import-export/ExportOffices'

// Auth components
import Login from './components/auth/Login'
import ProtectedRoute from './components/auth/ProtectedRoute'
import AuthTest from './components/auth/AuthTest'
import TestLogin from './components/auth/TestLogin'

// Landing Page
import LandingPage from './components/landing/LandingPage'

// Dashboard components
import Dashboard from './components/dashboard/Dashboard'
import UniversityDashboard from './components/dashboard/UniversityDashboard'

// KPI Management components
import KPIManagement from './components/kpi-management/KPIManagement'
import KPIDefinitionDashboard from './components/kpi-management/KPIDefinitionDashboard'
import ThemeList from './components/kpi-management/themes/ThemeList'
import ThemeDetail from './components/kpi-management/themes/ThemeDetail'
import ThemeForm from './components/kpi-management/themes/ThemeForm'
import SubThemeList from './components/kpi-management/subthemes/SubThemeList'
import SubThemeDetail from './components/kpi-management/subthemes/SubThemeDetail'
import SubThemeForm from './components/kpi-management/subthemes/SubThemeForm'
import AcademicYearList from './components/kpi-management/academic-years/AcademicYearList'
import AcademicYearForm from './components/kpi-management/academic-years/AcademicYearForm'
import MeasurementUnitList from './components/kpi-management/measurement-units/MeasurementUnitList'
import MeasurementUnitForm from './components/kpi-management/measurement-units/MeasurementUnitForm'
import KPIList from './components/kpi-management/kpis/KPIList'
import KPIDetail from './components/kpi-management/kpis/KPIDetail'
import KPIForm from './components/kpi-management/kpis/KPIForm'
import KPIImport from './components/kpi-management/kpis/KPIImport'
import AssignmentList from './components/kpi-management/assignments/AssignmentList'
import AssignmentDetail from './components/kpi-management/assignments/AssignmentDetail'
import AssignmentForm from './components/kpi-management/assignments/AssignmentForm'
import KPIReportList from './components/kpi-management/reports/KPIReportList'
import KPIReportDetail from './components/kpi-management/reports/KPIReportDetail'
import KPIReportForm from './components/kpi-management/reports/KPIReportForm'

// Office Management components
import OfficeManagement from './components/office-management/OfficeManagement'
import OfficeList from './components/office-management/offices/OfficeList'
import OfficeDetail from './components/office-management/offices/OfficeDetail'
import OfficeForm from './components/office-management/offices/OfficeForm'
// Position components removed
import OfficeKPIAssignmentList from './components/office-management/office-kpi-assignments/OfficeKPIAssignmentList'
import OfficeKPIAssignmentForm from './components/office-management/office-kpi-assignments/OfficeKPIAssignmentForm'
import OfficeKPIAssignmentDetail from './components/office-management/office-kpi-assignments/OfficeKPIAssignmentDetail'
import UserKPIAssignmentList from './components/office-management/user-kpi-assignments/UserKPIAssignmentList'
import UserKPIAssignmentForm from './components/office-management/user-kpi-assignments/UserKPIAssignmentForm'
import UserKPIAssignmentDetail from './components/office-management/user-kpi-assignments/UserKPIAssignmentDetail'
import OfficeKPIReportList from './components/office-management/reports/OfficeKPIReportList'
import OfficeKPIReportDetail from './components/office-management/reports/OfficeKPIReportDetail'
import OfficeKPIReportForm from './components/office-management/reports/OfficeKPIReportForm'
import UserKPIReportList from './components/user-management/reports/UserKPIReportList'
import UserKPIReportDetail from './components/user-management/reports/UserKPIReportDetail'
import UserKPIReportForm from './components/user-management/reports/UserKPIReportForm'

// Reporting Dashboard
import ReportingDashboard from './components/reporting/ReportingDashboard'
import AssignmentDashboard from './components/kpi-management/assignments/AssignmentDashboard'

// University components
import UniversityList from './components/universities/UniversityList'
import UniversityDetail from './components/universities/UniversityDetail'
import UniversityForm from './components/universities/UniversityForm'

// User Management components
import UserList from './components/user-management/UserList'
import UserDetail from './components/user-management/UserDetail'
import UserForm from './components/user-management/UserForm'

// Settings components
import Settings from './components/settings/Settings'
import SettingsDashboard from './components/settings/SettingsDashboard'
import TagList from './components/settings/tags/TagList'
import TagForm from './components/settings/tags/TagForm'
import UniversityClassifications from './components/settings/UniversityClassifications'
import UniversityClassificationForm from './components/settings/UniversityClassificationForm'
import SMTPSettings from './components/settings/smtp/SMTPSettings'
import LDAPSettings from './components/settings/ldap/LDAPSettings'

// API Documentation
import APIDocumentation from './components/api/APIDocumentation'

// AppContent component to handle loading state
const AppContent = () => {
  const { loading } = useAuth();
  const location = useLocation();
  const [initialLoad, setInitialLoad] = useState(true);

  // Set initialLoad to false after first render
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoad(false);
    }, 1000); // Wait for 1 second to ensure smooth transition

    return () => clearTimeout(timer);
  }, []);

  // Show loading overlay during authentication or initial load
  const showLoading = loading || initialLoad;

  return (
    <TenantProvider>
      {/* Global loading overlay */}
      <GlobalLoadingOverlay
        loading={showLoading}
        message="Loading your dashboard..."
      />

      {/* Dynamic Favicon that updates based on tenant */}
      <DynamicFavicon />

      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/login" element={<Login />} />
        <Route path="/auth-test" element={<AuthTest />} />
        <Route path="/test-login" element={<TestLogin />} />

        <Route element={<Layout />}>
          {/* Dashboard Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />

          {/* KPI Management Routes - Staff Only */}
          <Route
            path="/kpi-management"
            element={
              <ProtectedRoute requiredRole="staff">
                <KPIManagement />
              </ProtectedRoute>
            }
          />

          {/* KPI Definition Dashboard */}
          <Route
            path="/kpi-definition"
            element={
              <ProtectedRoute requiredRole="staff">
                <KPIDefinitionDashboard />
              </ProtectedRoute>
            }
          />

          {/* Theme Routes */}
          <Route
            path="/themes"
            element={
              <ProtectedRoute requiredRole="staff">
                <ThemeList />
              </ProtectedRoute>
            }
          />
          <Route
            path="/themes/:id"
            element={
              <ProtectedRoute requiredRole="staff">
                <ThemeDetail />
              </ProtectedRoute>
            }
          />
            <Route
              path="/themes/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <ThemeForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/themes/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <ThemeForm />
                </ProtectedRoute>
              }
            />

            {/* SubTheme Routes */}
            <Route
              path="/subthemes"
              element={
                <ProtectedRoute requiredRole="staff">
                  <SubThemeList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/subthemes/:id"
              element={
                <ProtectedRoute requiredRole="staff">
                  <SubThemeDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/subthemes/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <SubThemeForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/subthemes/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <SubThemeForm />
                </ProtectedRoute>
              }
            />

            {/* Academic Year Routes */}
            <Route
              path="/academic-years"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AcademicYearList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/academic-years/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AcademicYearForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/academic-years/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AcademicYearForm />
                </ProtectedRoute>
              }
            />

            {/* Measurement Unit Routes */}
            <Route
              path="/measurement-units"
              element={
                <ProtectedRoute requiredRole="staff">
                  <MeasurementUnitList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/measurement-units/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <MeasurementUnitForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/measurement-units/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <MeasurementUnitForm />
                </ProtectedRoute>
              }
            />

            {/* KPI Routes */}
            <Route
              path="/kpis"
              element={
                <ProtectedRoute requiredRole="staff">
                  <KPIList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/kpis/:id"
              element={
                <ProtectedRoute requiredRole="staff">
                  <KPIDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/kpis/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <KPIForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/kpis/import"
              element={
                <ProtectedRoute requiredRole="staff">
                  <KPIImport />
                </ProtectedRoute>
              }
            />
            <Route
              path="/kpis/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <KPIForm />
                </ProtectedRoute>
              }
            />

            {/* Assignment Routes */}
            <Route
              path="/assignments"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AssignmentList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assignments/:id"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AssignmentDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assignments/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AssignmentForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assignments/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <AssignmentForm />
                </ProtectedRoute>
              }
            />

            {/* University KPI Report Routes */}
            <Route
              path="/reports"
              element={
                <ProtectedRoute>
                  <KPIReportList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports/create"
              element={
                <ProtectedRoute>
                  <KPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports/:id/edit"
              element={
                <ProtectedRoute>
                  <KPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports/:id"
              element={
                <ProtectedRoute>
                  <KPIReportDetail />
                </ProtectedRoute>
              }
            />

            {/* University Routes */}
            <Route
              path="/universities"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityList />
                </ProtectedRoute>
              }
            />
            {/* Public route for university creation */}
            <Route
              path="/universities/create"
              element={<UniversityForm />}
            />
            <Route
              path="/universities/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/universities/:id"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityDetail />
                </ProtectedRoute>
              }
            />



            {/* Office Routes */}
            <Route
              path="/offices"
              element={
                <ProtectedRoute>
                  <OfficeList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/offices/create"
              element={
                <ProtectedRoute>
                  <OfficeForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/offices/import"
              element={
                <ProtectedRoute>
                  <Navigate to="/data-tools/import-export/offices" replace />
                </ProtectedRoute>
              }
            />
            <Route
              path="/offices/:id/edit"
              element={
                <ProtectedRoute>
                  <OfficeForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/offices/:id"
              element={
                <ProtectedRoute>
                  <OfficeDetail />
                </ProtectedRoute>
              }
            />

            {/* Position Routes removed */}

            {/* Office KPI Assignment Routes */}
            <Route
              path="/office-kpi-assignments"
              element={
                <ProtectedRoute>
                  <OfficeKPIAssignmentList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-kpi-assignments/create"
              element={
                <ProtectedRoute>
                  <OfficeKPIAssignmentForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-kpi-assignments/:id"
              element={
                <ProtectedRoute>
                  <OfficeKPIAssignmentDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-kpi-assignments/:id/edit"
              element={
                <ProtectedRoute>
                  <OfficeKPIAssignmentForm />
                </ProtectedRoute>
              }
            />

            {/* User KPI Assignment Routes */}
            <Route
              path="/user-kpi-assignments"
              element={
                <ProtectedRoute>
                  <UserKPIAssignmentList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-kpi-assignments/create"
              element={
                <ProtectedRoute>
                  <UserKPIAssignmentForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-kpi-assignments/:id"
              element={
                <ProtectedRoute>
                  <UserKPIAssignmentDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-kpi-assignments/:id/edit"
              element={
                <ProtectedRoute>
                  <UserKPIAssignmentForm />
                </ProtectedRoute>
              }
            />

            {/* Reporting Dashboard */}
            <Route
              path="/reporting"
              element={
                <ProtectedRoute>
                  <ReportingDashboard />
                </ProtectedRoute>
              }
            />

            {/* Assignment Dashboard */}
            <Route
              path="/assignments-dashboard"
              element={
                <ProtectedRoute>
                  <AssignmentDashboard />
                </ProtectedRoute>
              }
            />

            {/* Office KPI Report Routes */}
            <Route
              path="/office-reports"
              element={
                <ProtectedRoute>
                  <OfficeKPIReportList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-reports/create"
              element={
                <ProtectedRoute>
                  <OfficeKPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-reports/:id/edit"
              element={
                <ProtectedRoute>
                  <OfficeKPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/office-reports/:id"
              element={
                <ProtectedRoute>
                  <OfficeKPIReportDetail />
                </ProtectedRoute>
              }
            />

            {/* User KPI Report Routes */}
            <Route
              path="/user-reports"
              element={
                <ProtectedRoute>
                  <UserKPIReportList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-reports/create"
              element={
                <ProtectedRoute>
                  <UserKPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-reports/:id/edit"
              element={
                <ProtectedRoute>
                  <UserKPIReportForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user-reports/:id"
              element={
                <ProtectedRoute>
                  <UserKPIReportDetail />
                </ProtectedRoute>
              }
            />

            {/* User Management Routes */}
            <Route
              path="/users"
              element={
                <ProtectedRoute>
                  <UserList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/users/create"
              element={
                <ProtectedRoute>
                  <UserForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/users/import"
              element={
                <ProtectedRoute>
                  <Navigate to="/data-tools/import-export/users" replace />
                </ProtectedRoute>
              }
            />
            <Route
              path="/users/:id/edit"
              element={
                <ProtectedRoute>
                  <UserForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/users/:id"
              element={
                <ProtectedRoute>
                  <UserDetail />
                </ProtectedRoute>
              }
            />

            {/* Settings Routes */}
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <SettingsDashboard />
                </ProtectedRoute>
              }
            />

            {/* Tag Management Routes */}
            <Route
              path="/tag"
              element={
                <ProtectedRoute requiredRole="staff">
                  <TagList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/tag/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <TagForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/tag/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <TagForm />
                </ProtectedRoute>
              }
            />

            {/* University Classification Routes */}
            <Route
              path="/settings/university-classifications"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityClassifications />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings/university-classifications/create"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityClassificationForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings/university-classifications/:id/edit"
              element={
                <ProtectedRoute requiredRole="staff">
                  <UniversityClassificationForm />
                </ProtectedRoute>
              }
            />

            {/* SMTP Settings Route */}
            <Route
              path="/settings/smtp"
              element={
                <ProtectedRoute requiredRole="staff">
                  <SMTPSettings />
                </ProtectedRoute>
              }
            />

            {/* LDAP Settings Route */}
            <Route
              path="/settings/ldap"
              element={
                <ProtectedRoute requiredRole="staff">
                  <LDAPSettings />
                </ProtectedRoute>
              }
            />

            {/* API Documentation Route */}
            <Route
              path="/api-documentation"
              element={
                <ProtectedRoute>
                  <APIDocumentation />
                </ProtectedRoute>
              }
            />

            {/* Data Tools Routes */}
            <Route
              path="/data-tools"
              element={
                <ProtectedRoute>
                  <DataToolsDashboard />
                </ProtectedRoute>
              }
            />

            {/* Import & Export Routes */}
            <Route
              path="/data-tools/import-export"
              element={
                <ProtectedRoute>
                  <ImportExportDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/import-export/kpis"
              element={
                <ProtectedRoute>
                  <KPIImport />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/import-export/users"
              element={
                <ProtectedRoute>
                  <UserImport />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/import-export/export-users"
              element={
                <ProtectedRoute>
                  <ExportUsers />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/import-export/offices"
              element={
                <ProtectedRoute>
                  <OfficeImport />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/import-export/export-offices"
              element={
                <ProtectedRoute>
                  <ExportOffices />
                </ProtectedRoute>
              }
            />

            {/* Validation Tools Routes */}
            <Route
              path="/data-tools/validation"
              element={
                <ProtectedRoute>
                  <ValidationDashboard />
                </ProtectedRoute>
              }
            />

            {/* Backup & Restore Routes */}
            <Route
              path="/data-tools/backup"
              element={
                <ProtectedRoute>
                  <BackupDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/backup/create"
              element={
                <ProtectedRoute>
                  <CreateBackup />
                </ProtectedRoute>
              }
            />

            <Route
              path="/data-tools/backup/restore"
              element={
                <ProtectedRoute>
                  <RestoreBackup />
                </ProtectedRoute>
              }
            />

            {/* Data Sync Routes */}
            <Route
              path="/data-tools/sync"
              element={
                <ProtectedRoute>
                  <SyncDashboard />
                </ProtectedRoute>
              }
            />

            {/* Preview & Simulation Routes */}
            <Route
              path="/data-tools/preview"
              element={
                <ProtectedRoute>
                  <PreviewDashboard />
                </ProtectedRoute>
              }
            />

            {/* Audit Logs & History Routes */}
            <Route
              path="/data-tools/audit"
              element={
                <ProtectedRoute>
                  <AuditDashboard />
                </ProtectedRoute>
              }
            />

          </Route>

          {/* Catch-all route - redirect to login for unauthenticated users, dashboard for authenticated users */}
          <Route path="*" element={<CatchAllRoute />} />
        </Routes>
        <SessionExpiringDialog />
      </TenantProvider>
    );
};

function App() {
  // Run tests on component mount
  useEffect(() => {
    console.log('Running KPI utility tests...');
    runTests();
  }, []);

  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
