import {
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON>s<PERSON>ighlight,
  <PERSON>Legend,
  ChartsTooltip,
  DEFAULT_X_AXIS_KEY,
  DrawingContext,
  InteractionContext,
  ResponsiveChartContainer,
  SeriesContext,
  animated,
  arc_default,
  getIsFaded,
  getIsHighlighted,
  getPercentageValue,
  to,
  useInteractionItemProps,
  useTransition
} from "./chunk-TWO4WIIF.js";
import {
  _objectWithoutPropertiesLoose,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  init_composeClasses,
  init_generateUtilityClass,
  init_generateUtilityClasses,
  init_objectWithoutPropertiesLoose,
  require_prop_types,
  styled_default
} from "./chunk-CNC4IL3C.js";
import {
  _extends,
  init_extends
} from "./chunk-TYEPBG5V.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/x-charts/esm/PieChart/PieChart.js
init_extends();
var React7 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/PieChart/PiePlot.js
var React6 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/PieChart/PieArcPlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React3 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/PieChart/PieArc.js
init_extends();
init_objectWithoutPropertiesLoose();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["id", "dataIndex", "classes", "color", "highlightScope", "onClick", "isFaded", "isHighlighted", "startAngle", "endAngle", "paddingAngle", "innerRadius", "outerRadius", "cornerRadius"];
function getPieArcUtilityClass(slot) {
  return generateUtilityClass("MuiPieArc", slot);
}
var pieArcClasses = generateUtilityClasses("MuiPieArc", ["root", "highlighted", "faded"]);
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getPieArcUtilityClass, classes);
};
var PieArcRoot = styled_default(animated.path, {
  name: "MuiPieArc",
  slot: "Root",
  overridesResolver: (_, styles) => styles.arc
})(({
  theme
}) => ({
  stroke: (theme.vars || theme).palette.background.paper,
  strokeWidth: 1,
  strokeLinejoin: "round"
}));
function PieArc(props) {
  const {
    id,
    dataIndex,
    classes: innerClasses,
    color,
    highlightScope,
    onClick,
    isFaded,
    isHighlighted,
    startAngle,
    endAngle,
    paddingAngle,
    innerRadius,
    outerRadius,
    cornerRadius
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const ownerState = {
    id,
    dataIndex,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses(ownerState);
  const getInteractionItemProps = useInteractionItemProps(highlightScope);
  return (0, import_jsx_runtime.jsx)(PieArcRoot, _extends({
    d: to([startAngle, endAngle, paddingAngle, innerRadius, outerRadius, cornerRadius], (sA, eA, pA, iR, oR, cR) => arc_default().cornerRadius(cR)({
      padAngle: pA,
      startAngle: sA,
      endAngle: eA,
      innerRadius: iR,
      outerRadius: oR
    })),
    onClick,
    cursor: onClick ? "pointer" : "unset",
    ownerState,
    className: classes.root
  }, other, getInteractionItemProps({
    type: "pie",
    seriesId: id,
    dataIndex
  })));
}
true ? PieArc.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types.default.object,
  dataIndex: import_prop_types.default.number.isRequired,
  highlightScope: import_prop_types.default.shape({
    faded: import_prop_types.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]).isRequired,
  isFaded: import_prop_types.default.bool.isRequired,
  isHighlighted: import_prop_types.default.bool.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/dataTransform/transition.js
var defaultTransitionConfig = {
  keys: (item) => item.id,
  from: ({
    innerRadius,
    outerRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    color,
    isFaded
  }) => ({
    innerRadius,
    outerRadius: (innerRadius + outerRadius) / 2,
    cornerRadius,
    startAngle: (startAngle + endAngle) / 2,
    endAngle: (startAngle + endAngle) / 2,
    paddingAngle,
    fill: color,
    opacity: isFaded ? 0.3 : 1
  }),
  leave: ({
    innerRadius,
    startAngle,
    endAngle
  }) => ({
    innerRadius,
    outerRadius: innerRadius,
    startAngle: (startAngle + endAngle) / 2,
    endAngle: (startAngle + endAngle) / 2
  }),
  enter: ({
    innerRadius,
    outerRadius,
    startAngle,
    endAngle
  }) => ({
    innerRadius,
    outerRadius,
    startAngle,
    endAngle
  }),
  update: ({
    innerRadius,
    outerRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    color,
    isFaded
  }) => ({
    innerRadius,
    outerRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    fill: color,
    opacity: isFaded ? 0.3 : 1
  }),
  config: {
    tension: 120,
    friction: 14,
    clamp: true
  }
};
var defaultLabelTransitionConfig = {
  keys: (item) => item.id,
  from: ({
    innerRadius,
    outerRadius,
    arcLabelRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle
  }) => ({
    innerRadius,
    outerRadius: (innerRadius + outerRadius) / 2,
    cornerRadius,
    arcLabelRadius,
    startAngle: (startAngle + endAngle) / 2,
    endAngle: (startAngle + endAngle) / 2,
    paddingAngle,
    opacity: 0
  }),
  leave: ({
    innerRadius,
    startAngle,
    endAngle
  }) => ({
    innerRadius,
    outerRadius: innerRadius,
    arcLabelRadius: innerRadius,
    startAngle: (startAngle + endAngle) / 2,
    endAngle: (startAngle + endAngle) / 2,
    opacity: 0
  }),
  enter: ({
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    arcLabelRadius
  }) => ({
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    arcLabelRadius,
    opacity: 1
  }),
  update: ({
    innerRadius,
    outerRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    arcLabelRadius
  }) => ({
    innerRadius,
    outerRadius,
    cornerRadius,
    startAngle,
    endAngle,
    paddingAngle,
    arcLabelRadius,
    opacity: 1
  }),
  config: {
    tension: 120,
    friction: 14,
    clamp: true
  }
};

// node_modules/@mui/x-charts/esm/PieChart/dataTransform/useTransformData.js
init_extends();
var React2 = __toESM(require_react());
function useTransformData(series) {
  const {
    id: seriesId,
    highlightScope,
    data,
    faded,
    highlighted,
    paddingAngle: basePaddingAngle = 0,
    innerRadius: baseInnerRadius = 0,
    arcLabelRadius: baseArcLabelRadius,
    outerRadius: baseOuterRadius,
    cornerRadius: baseCornerRadius = 0
  } = series;
  const {
    item: highlightedItem
  } = React2.useContext(InteractionContext);
  const getHighlightStatus = React2.useCallback((dataIndex) => {
    const isHighlighted = getIsHighlighted(highlightedItem, {
      type: "pie",
      seriesId,
      dataIndex
    }, highlightScope);
    const isFaded = !isHighlighted && getIsFaded(highlightedItem, {
      type: "pie",
      seriesId,
      dataIndex
    }, highlightScope);
    return {
      isHighlighted,
      isFaded
    };
  }, [highlightScope, highlightedItem, seriesId]);
  const dataWithHighlight = React2.useMemo(() => data.map((item, itemIndex) => {
    var _attributesOverride$p, _attributesOverride$i, _attributesOverride$o, _attributesOverride$c, _ref, _attributesOverride$a;
    const {
      isHighlighted,
      isFaded
    } = getHighlightStatus(itemIndex);
    const attributesOverride = _extends({
      additionalRadius: 0
    }, isFaded && faded || isHighlighted && highlighted || {});
    const paddingAngle = Math.max(0, Math.PI * ((_attributesOverride$p = attributesOverride.paddingAngle) != null ? _attributesOverride$p : basePaddingAngle) / 180);
    const innerRadius = Math.max(0, (_attributesOverride$i = attributesOverride.innerRadius) != null ? _attributesOverride$i : baseInnerRadius);
    const outerRadius = Math.max(0, (_attributesOverride$o = attributesOverride.outerRadius) != null ? _attributesOverride$o : baseOuterRadius + attributesOverride.additionalRadius);
    const cornerRadius = (_attributesOverride$c = attributesOverride.cornerRadius) != null ? _attributesOverride$c : baseCornerRadius;
    const arcLabelRadius = (_ref = (_attributesOverride$a = attributesOverride.arcLabelRadius) != null ? _attributesOverride$a : baseArcLabelRadius) != null ? _ref : (innerRadius + outerRadius) / 2;
    return _extends({}, item, attributesOverride, {
      isFaded,
      isHighlighted,
      paddingAngle,
      innerRadius,
      outerRadius,
      cornerRadius,
      arcLabelRadius
    });
  }), [baseCornerRadius, baseInnerRadius, baseOuterRadius, basePaddingAngle, baseArcLabelRadius, data, faded, getHighlightStatus, highlighted]);
  return dataWithHighlight;
}

// node_modules/@mui/x-charts/esm/PieChart/PieArcPlot.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded2 = ["slots", "slotProps", "innerRadius", "outerRadius", "cornerRadius", "paddingAngle", "id", "highlightScope", "highlighted", "faded", "data", "onClick", "skipAnimation"];
var _excluded22 = ["startAngle", "endAngle", "paddingAngle", "innerRadius", "arcLabelRadius", "outerRadius", "cornerRadius"];
function PieArcPlot(props) {
  var _slots$pieArc;
  const {
    slots,
    slotProps,
    innerRadius = 0,
    outerRadius,
    cornerRadius = 0,
    paddingAngle = 0,
    id,
    highlightScope,
    highlighted,
    faded = {
      additionalRadius: -5
    },
    data,
    onClick,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const transformedData = useTransformData({
    innerRadius,
    outerRadius,
    cornerRadius,
    paddingAngle,
    id,
    highlightScope,
    highlighted,
    faded,
    data
  });
  const transition = useTransition(transformedData, _extends({}, defaultTransitionConfig, {
    immediate: skipAnimation
  }));
  if (data.length === 0) {
    return null;
  }
  const Arc = (_slots$pieArc = slots == null ? void 0 : slots.pieArc) != null ? _slots$pieArc : PieArc;
  return (0, import_jsx_runtime2.jsx)("g", _extends({}, other, {
    children: transition((_ref, item, _, index) => {
      let {
        startAngle,
        endAngle,
        paddingAngle: pA,
        innerRadius: iR,
        outerRadius: oR,
        cornerRadius: cR
      } = _ref, style = _objectWithoutPropertiesLoose(_ref, _excluded22);
      return (0, import_jsx_runtime2.jsx)(Arc, _extends({
        startAngle,
        endAngle,
        paddingAngle: pA,
        innerRadius: iR,
        outerRadius: oR,
        cornerRadius: cR,
        style,
        id,
        color: item.color,
        dataIndex: index,
        highlightScope,
        isFaded: item.isFaded,
        isHighlighted: item.isHighlighted,
        onClick: onClick && ((event) => {
          onClick(event, {
            type: "pie",
            seriesId: id,
            dataIndex: index
          }, item);
        })
      }, slotProps == null ? void 0 : slotProps.pieArc));
    })
  }));
}
true ? PieArcPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The radius between circle center and the arc label in px.
   * @default (innerRadius - outerRadius) / 2
   */
  arcLabelRadius: import_prop_types2.default.number,
  /**
   * The radius applied to arc corners (similar to border radius).
   * @default 0
   */
  cornerRadius: import_prop_types2.default.number,
  data: import_prop_types2.default.arrayOf(import_prop_types2.default.shape({
    color: import_prop_types2.default.string.isRequired,
    endAngle: import_prop_types2.default.number.isRequired,
    formattedValue: import_prop_types2.default.string.isRequired,
    id: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]).isRequired,
    index: import_prop_types2.default.number.isRequired,
    label: import_prop_types2.default.string,
    padAngle: import_prop_types2.default.number.isRequired,
    startAngle: import_prop_types2.default.number.isRequired,
    value: import_prop_types2.default.number.isRequired
  })).isRequired,
  /**
   * Override the arc attibutes when it is faded.
   * @default { additionalRadius: -5 }
   */
  faded: import_prop_types2.default.shape({
    additionalRadius: import_prop_types2.default.number,
    arcLabelRadius: import_prop_types2.default.number,
    color: import_prop_types2.default.string,
    cornerRadius: import_prop_types2.default.number,
    innerRadius: import_prop_types2.default.number,
    outerRadius: import_prop_types2.default.number,
    paddingAngle: import_prop_types2.default.number
  }),
  /**
   * Override the arc attibutes when it is highlighted.
   */
  highlighted: import_prop_types2.default.shape({
    additionalRadius: import_prop_types2.default.number,
    arcLabelRadius: import_prop_types2.default.number,
    color: import_prop_types2.default.string,
    cornerRadius: import_prop_types2.default.number,
    innerRadius: import_prop_types2.default.number,
    outerRadius: import_prop_types2.default.number,
    paddingAngle: import_prop_types2.default.number
  }),
  highlightScope: import_prop_types2.default.shape({
    faded: import_prop_types2.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types2.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]).isRequired,
  /**
   * The radius between circle center and the begining of the arc.
   * @default 0
   */
  innerRadius: import_prop_types2.default.number,
  /**
   * Callback fired when a pie item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.
   * @param {DefaultizedPieValueType} item The pie item.
   */
  onClick: import_prop_types2.default.func,
  /**
   * The radius between circle center and the end of the arc.
   */
  outerRadius: import_prop_types2.default.number.isRequired,
  /**
   * The padding angle (deg) between two arcs.
   * @default 0
   */
  paddingAngle: import_prop_types2.default.number,
  /**
   * If `true`, animations are skiped.
   * @default false
   */
  skipAnimation: import_prop_types2.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types2.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types2.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabelPlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React5 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabel.js
init_extends();
init_objectWithoutPropertiesLoose();
var React4 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded3 = ["id", "classes", "color", "startAngle", "endAngle", "paddingAngle", "arcLabelRadius", "innerRadius", "outerRadius", "cornerRadius", "formattedArcLabel", "isHighlighted", "isFaded", "style"];
function getPieArcLabelUtilityClass(slot) {
  return generateUtilityClass("MuiPieArcLabel", slot);
}
var pieArcLabelClasses = generateUtilityClasses("MuiPieArcLabel", ["root", "highlighted", "faded"]);
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getPieArcLabelUtilityClass, classes);
};
var PieArcLabelRoot = styled_default(animated.text, {
  name: "MuiPieArcLabel",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  theme
}) => ({
  fill: (theme.vars || theme).palette.text.primary,
  textAnchor: "middle",
  dominantBaseline: "middle"
}));
var getLabelPosition = (formattedArcLabel, variable) => (startAngle, endAngle, padAngle, arcLabelRadius, cornerRadius) => {
  if (!formattedArcLabel) {
    return 0;
  }
  const [x, y] = arc_default().cornerRadius(cornerRadius).centroid({
    padAngle,
    startAngle,
    endAngle,
    innerRadius: arcLabelRadius,
    outerRadius: arcLabelRadius
  });
  if (variable === "x") {
    return x;
  }
  return y;
};
function PieArcLabel(props) {
  const {
    id,
    classes: innerClasses,
    color,
    startAngle,
    endAngle,
    paddingAngle,
    arcLabelRadius,
    cornerRadius,
    formattedArcLabel,
    isHighlighted,
    isFaded,
    style
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const ownerState = {
    id,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses2(ownerState);
  return (0, import_jsx_runtime3.jsx)(PieArcLabelRoot, _extends({
    className: classes.root
  }, other, {
    style: _extends({
      x: to([startAngle, endAngle, paddingAngle, arcLabelRadius, cornerRadius], getLabelPosition(formattedArcLabel, "x")),
      y: to([startAngle, endAngle, paddingAngle, arcLabelRadius, cornerRadius], getLabelPosition(formattedArcLabel, "y"))
    }, style),
    children: formattedArcLabel
  }));
}
true ? PieArcLabel.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types3.default.object,
  formattedArcLabel: import_prop_types3.default.string,
  id: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]).isRequired,
  isFaded: import_prop_types3.default.bool.isRequired,
  isHighlighted: import_prop_types3.default.bool.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabelPlot.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var _excluded4 = ["slots", "slotProps", "innerRadius", "outerRadius", "arcLabelRadius", "cornerRadius", "paddingAngle", "id", "highlightScope", "highlighted", "faded", "data", "arcLabel", "arcLabelMinAngle", "skipAnimation"];
var _excluded23 = ["startAngle", "endAngle", "paddingAngle", "innerRadius", "outerRadius", "arcLabelRadius", "cornerRadius"];
var RATIO = 180 / Math.PI;
function getItemLabel(arcLabel, arcLabelMinAngle, item) {
  if (!arcLabel) {
    return null;
  }
  const angle = (item.endAngle - item.startAngle) * RATIO;
  if (angle < arcLabelMinAngle) {
    return null;
  }
  if (typeof arcLabel === "string") {
    var _item$arcLabel;
    return (_item$arcLabel = item[arcLabel]) == null ? void 0 : _item$arcLabel.toString();
  }
  return arcLabel(item);
}
function PieArcLabelPlot(props) {
  var _slots$pieArcLabel;
  const {
    slots,
    slotProps,
    innerRadius,
    outerRadius,
    arcLabelRadius,
    cornerRadius = 0,
    paddingAngle = 0,
    id,
    highlightScope,
    highlighted,
    faded = {
      additionalRadius: -5
    },
    data,
    arcLabel,
    arcLabelMinAngle = 0,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const transformedData = useTransformData({
    innerRadius,
    outerRadius,
    arcLabelRadius,
    cornerRadius,
    paddingAngle,
    id,
    highlightScope,
    highlighted,
    faded,
    data
  });
  const transition = useTransition(transformedData, _extends({}, defaultLabelTransitionConfig, {
    immediate: skipAnimation
  }));
  if (data.length === 0) {
    return null;
  }
  const ArcLabel = (_slots$pieArcLabel = slots == null ? void 0 : slots.pieArcLabel) != null ? _slots$pieArcLabel : PieArcLabel;
  return (0, import_jsx_runtime4.jsx)("g", _extends({}, other, {
    children: transition((_ref, item) => {
      let {
        startAngle,
        endAngle,
        paddingAngle: pA,
        innerRadius: iR,
        outerRadius: oR,
        arcLabelRadius: aLR,
        cornerRadius: cR
      } = _ref, style = _objectWithoutPropertiesLoose(_ref, _excluded23);
      return (0, import_jsx_runtime4.jsx)(ArcLabel, _extends({
        startAngle,
        endAngle,
        paddingAngle: pA,
        innerRadius: iR,
        outerRadius: oR,
        arcLabelRadius: aLR,
        cornerRadius: cR,
        style,
        id,
        color: item.color,
        isFaded: item.isFaded,
        isHighlighted: item.isHighlighted,
        formattedArcLabel: getItemLabel(arcLabel, arcLabelMinAngle, item)
      }, slotProps == null ? void 0 : slotProps.pieArcLabel));
    })
  }));
}
true ? PieArcLabelPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The label displayed into the arc.
   */
  arcLabel: import_prop_types4.default.oneOfType([import_prop_types4.default.oneOf(["formattedValue", "label", "value"]), import_prop_types4.default.func]),
  /**
   * The minimal angle required to display the arc label.
   * @default 0
   */
  arcLabelMinAngle: import_prop_types4.default.number,
  /**
   * The radius between circle center and the arc label in px.
   * @default (innerRadius - outerRadius) / 2
   */
  arcLabelRadius: import_prop_types4.default.number,
  /**
   * The radius applied to arc corners (similar to border radius).
   * @default 0
   */
  cornerRadius: import_prop_types4.default.number,
  data: import_prop_types4.default.arrayOf(import_prop_types4.default.shape({
    color: import_prop_types4.default.string.isRequired,
    endAngle: import_prop_types4.default.number.isRequired,
    formattedValue: import_prop_types4.default.string.isRequired,
    id: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.string]).isRequired,
    index: import_prop_types4.default.number.isRequired,
    label: import_prop_types4.default.string,
    padAngle: import_prop_types4.default.number.isRequired,
    startAngle: import_prop_types4.default.number.isRequired,
    value: import_prop_types4.default.number.isRequired
  })).isRequired,
  /**
   * Override the arc attibutes when it is faded.
   * @default { additionalRadius: -5 }
   */
  faded: import_prop_types4.default.shape({
    additionalRadius: import_prop_types4.default.number,
    arcLabelRadius: import_prop_types4.default.number,
    color: import_prop_types4.default.string,
    cornerRadius: import_prop_types4.default.number,
    innerRadius: import_prop_types4.default.number,
    outerRadius: import_prop_types4.default.number,
    paddingAngle: import_prop_types4.default.number
  }),
  /**
   * Override the arc attibutes when it is highlighted.
   */
  highlighted: import_prop_types4.default.shape({
    additionalRadius: import_prop_types4.default.number,
    arcLabelRadius: import_prop_types4.default.number,
    color: import_prop_types4.default.string,
    cornerRadius: import_prop_types4.default.number,
    innerRadius: import_prop_types4.default.number,
    outerRadius: import_prop_types4.default.number,
    paddingAngle: import_prop_types4.default.number
  }),
  highlightScope: import_prop_types4.default.shape({
    faded: import_prop_types4.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types4.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.string]).isRequired,
  /**
   * The radius between circle center and the begining of the arc.
   * @default 0
   */
  innerRadius: import_prop_types4.default.number,
  /**
   * The radius between circle center and the end of the arc.
   */
  outerRadius: import_prop_types4.default.number.isRequired,
  /**
   * The padding angle (deg) between two arcs.
   * @default 0
   */
  paddingAngle: import_prop_types4.default.number,
  /**
   * If `true`, animations are skiped.
   * @default false
   */
  skipAnimation: import_prop_types4.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types4.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types4.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PiePlot.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
function PiePlot(props) {
  const {
    skipAnimation,
    slots,
    slotProps,
    onClick
  } = props;
  const seriesData = React6.useContext(SeriesContext).pie;
  const {
    left,
    top,
    width,
    height
  } = React6.useContext(DrawingContext);
  if (seriesData === void 0) {
    return null;
  }
  const availableRadius = Math.min(width, height) / 2;
  const {
    series,
    seriesOrder
  } = seriesData;
  return (0, import_jsx_runtime6.jsxs)("g", {
    children: [seriesOrder.map((seriesId) => {
      const {
        innerRadius: innerRadiusParam,
        outerRadius: outerRadiusParam,
        cornerRadius,
        paddingAngle,
        data,
        cx: cxParam,
        cy: cyParam,
        highlighted,
        faded,
        highlightScope
      } = series[seriesId];
      const outerRadius = getPercentageValue(outerRadiusParam != null ? outerRadiusParam : availableRadius, availableRadius);
      const innerRadius = getPercentageValue(innerRadiusParam != null ? innerRadiusParam : 0, availableRadius);
      const cx = getPercentageValue(cxParam != null ? cxParam : "50%", width);
      const cy = getPercentageValue(cyParam != null ? cyParam : "50%", height);
      return (0, import_jsx_runtime5.jsx)("g", {
        transform: `translate(${left + cx}, ${top + cy})`,
        children: (0, import_jsx_runtime5.jsx)(PieArcPlot, {
          innerRadius,
          outerRadius,
          cornerRadius,
          paddingAngle,
          id: seriesId,
          data,
          skipAnimation,
          highlightScope,
          highlighted,
          faded,
          onClick,
          slots,
          slotProps
        })
      }, seriesId);
    }), seriesOrder.map((seriesId) => {
      const {
        innerRadius: innerRadiusParam,
        outerRadius: outerRadiusParam,
        arcLabelRadius: arcLabelRadiusParam,
        cornerRadius,
        paddingAngle,
        arcLabel,
        arcLabelMinAngle,
        data,
        cx: cxParam,
        cy: cyParam,
        highlightScope
      } = series[seriesId];
      const outerRadius = getPercentageValue(outerRadiusParam != null ? outerRadiusParam : availableRadius, availableRadius);
      const innerRadius = getPercentageValue(innerRadiusParam != null ? innerRadiusParam : 0, availableRadius);
      const arcLabelRadius = arcLabelRadiusParam === void 0 ? (outerRadius + innerRadius) / 2 : getPercentageValue(arcLabelRadiusParam, availableRadius);
      const cx = getPercentageValue(cxParam != null ? cxParam : "50%", width);
      const cy = getPercentageValue(cyParam != null ? cyParam : "50%", height);
      return (0, import_jsx_runtime5.jsx)("g", {
        transform: `translate(${left + cx}, ${top + cy})`,
        children: (0, import_jsx_runtime5.jsx)(PieArcLabelPlot, {
          innerRadius,
          outerRadius: outerRadius != null ? outerRadius : availableRadius,
          arcLabelRadius,
          cornerRadius,
          paddingAngle,
          id: seriesId,
          data,
          skipAnimation,
          arcLabel,
          arcLabelMinAngle,
          highlightScope
        })
      }, seriesId);
    })]
  });
}
true ? PiePlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when a pie item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.
   * @param {DefaultizedPieValueType} item The pie item.
   */
  onClick: import_prop_types5.default.func,
  /**
   * If `true`, animations are skiped.
   * @default false
   */
  skipAnimation: import_prop_types5.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types5.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types5.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieChart.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var defaultMargin = {
  top: 5,
  bottom: 5,
  left: 5,
  right: 100
};
function PieChart(props) {
  const {
    xAxis,
    yAxis,
    series,
    width,
    height,
    margin: marginProps,
    colors,
    sx,
    tooltip = {
      trigger: "item"
    },
    axisHighlight = {
      x: "none",
      y: "none"
    },
    skipAnimation,
    legend = {
      direction: "column",
      position: {
        vertical: "middle",
        horizontal: "right"
      }
    },
    topAxis = null,
    leftAxis = null,
    rightAxis = null,
    bottomAxis = null,
    children,
    slots,
    slotProps,
    onClick
  } = props;
  const margin = _extends({}, defaultMargin, marginProps);
  return (0, import_jsx_runtime8.jsxs)(ResponsiveChartContainer, {
    series: series.map((s) => _extends({
      type: "pie"
    }, s)),
    width,
    height,
    margin,
    xAxis: xAxis != null ? xAxis : [{
      id: DEFAULT_X_AXIS_KEY,
      scaleType: "point",
      data: [...new Array(Math.max(...series.map((s) => s.data.length)))].map((_, index) => index)
    }],
    yAxis,
    colors,
    sx,
    disableAxisListener: (tooltip == null ? void 0 : tooltip.trigger) !== "axis" && (axisHighlight == null ? void 0 : axisHighlight.x) === "none" && (axisHighlight == null ? void 0 : axisHighlight.y) === "none",
    children: [(0, import_jsx_runtime7.jsx)(ChartsAxis, {
      topAxis,
      leftAxis,
      rightAxis,
      bottomAxis,
      slots,
      slotProps
    }), (0, import_jsx_runtime7.jsx)(PiePlot, {
      slots,
      slotProps,
      onClick,
      skipAnimation
    }), (0, import_jsx_runtime7.jsx)(ChartsLegend, _extends({}, legend, {
      slots,
      slotProps
    })), (0, import_jsx_runtime7.jsx)(ChartsAxisHighlight, _extends({}, axisHighlight)), (0, import_jsx_runtime7.jsx)(ChartsTooltip, _extends({}, tooltip)), children]
  });
}
true ? PieChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  axisHighlight: import_prop_types6.default.shape({
    x: import_prop_types6.default.oneOf(["band", "line", "none"]),
    y: import_prop_types6.default.oneOf(["band", "line", "none"])
  }),
  /**
   * Indicate which axis to display the bottom of the charts.
   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.
   * @default null
   */
  bottomAxis: import_prop_types6.default.oneOfType([import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    position: import_prop_types6.default.oneOf(["bottom", "top"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number
  }), import_prop_types6.default.string]),
  children: import_prop_types6.default.node,
  className: import_prop_types6.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default blueberryTwilightPalette
   */
  colors: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.string), import_prop_types6.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types6.default.arrayOf(import_prop_types6.default.object),
  desc: import_prop_types6.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types6.default.bool,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   * @default undefined
   */
  height: import_prop_types6.default.number,
  /**
   * Indicate which axis to display the left of the charts.
   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.
   * @default null
   */
  leftAxis: import_prop_types6.default.oneOfType([import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    position: import_prop_types6.default.oneOf(["left", "right"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number
  }), import_prop_types6.default.string]),
  /**
   * @deprecated Consider using `slotProps.legend` instead.
   */
  legend: import_prop_types6.default.shape({
    classes: import_prop_types6.default.object,
    direction: import_prop_types6.default.oneOf(["column", "row"]),
    hidden: import_prop_types6.default.bool,
    position: import_prop_types6.default.shape({
      horizontal: import_prop_types6.default.oneOf(["left", "middle", "right"]).isRequired,
      vertical: import_prop_types6.default.oneOf(["bottom", "middle", "top"]).isRequired
    }),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object
  }),
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   * @default object Depends on the charts type.
   */
  margin: import_prop_types6.default.shape({
    bottom: import_prop_types6.default.number,
    left: import_prop_types6.default.number,
    right: import_prop_types6.default.number,
    top: import_prop_types6.default.number
  }),
  onClick: import_prop_types6.default.func,
  /**
   * Indicate which axis to display the right of the charts.
   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.
   * @default null
   */
  rightAxis: import_prop_types6.default.oneOfType([import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    position: import_prop_types6.default.oneOf(["left", "right"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number
  }), import_prop_types6.default.string]),
  series: import_prop_types6.default.arrayOf(import_prop_types6.default.shape({
    arcLabel: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["formattedValue", "label", "value"]), import_prop_types6.default.func]),
    arcLabelMinAngle: import_prop_types6.default.number,
    arcLabelRadius: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    color: import_prop_types6.default.string,
    cornerRadius: import_prop_types6.default.number,
    cx: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    cy: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    data: import_prop_types6.default.arrayOf(import_prop_types6.default.shape({
      color: import_prop_types6.default.string,
      id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
      label: import_prop_types6.default.string,
      value: import_prop_types6.default.number.isRequired
    })).isRequired,
    endAngle: import_prop_types6.default.number,
    faded: import_prop_types6.default.shape({
      additionalRadius: import_prop_types6.default.number,
      arcLabelRadius: import_prop_types6.default.number,
      color: import_prop_types6.default.string,
      cornerRadius: import_prop_types6.default.number,
      innerRadius: import_prop_types6.default.number,
      outerRadius: import_prop_types6.default.number,
      paddingAngle: import_prop_types6.default.number
    }),
    highlighted: import_prop_types6.default.shape({
      additionalRadius: import_prop_types6.default.number,
      arcLabelRadius: import_prop_types6.default.number,
      color: import_prop_types6.default.string,
      cornerRadius: import_prop_types6.default.number,
      innerRadius: import_prop_types6.default.number,
      outerRadius: import_prop_types6.default.number,
      paddingAngle: import_prop_types6.default.number
    }),
    highlightScope: import_prop_types6.default.shape({
      faded: import_prop_types6.default.oneOf(["global", "none", "series"]),
      highlighted: import_prop_types6.default.oneOf(["item", "none", "series"])
    }),
    id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    innerRadius: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    outerRadius: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    paddingAngle: import_prop_types6.default.number,
    sortingValues: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["asc", "desc", "none"]), import_prop_types6.default.func]),
    startAngle: import_prop_types6.default.number,
    type: import_prop_types6.default.oneOf(["pie"]),
    valueFormatter: import_prop_types6.default.func
  })).isRequired,
  /**
   * If `true`, animations are skiped.
   * @default false
   */
  skipAnimation: import_prop_types6.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  slots: import_prop_types6.default.object,
  sx: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.object, import_prop_types6.default.bool])), import_prop_types6.default.func, import_prop_types6.default.object]),
  title: import_prop_types6.default.string,
  tooltip: import_prop_types6.default.shape({
    axisContent: import_prop_types6.default.elementType,
    classes: import_prop_types6.default.object,
    itemContent: import_prop_types6.default.elementType,
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    trigger: import_prop_types6.default.oneOf(["axis", "item", "none"])
  }),
  /**
   * Indicate which axis to display the top of the charts.
   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.
   * @default null
   */
  topAxis: import_prop_types6.default.oneOfType([import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    position: import_prop_types6.default.oneOf(["bottom", "top"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number
  }), import_prop_types6.default.string]),
  viewBox: import_prop_types6.default.shape({
    height: import_prop_types6.default.number,
    width: import_prop_types6.default.number,
    x: import_prop_types6.default.number,
    y: import_prop_types6.default.number
  }),
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   * @default undefined
   */
  width: import_prop_types6.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.
   */
  xAxis: import_prop_types6.default.arrayOf(import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    data: import_prop_types6.default.array,
    dataKey: import_prop_types6.default.string,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    hideTooltip: import_prop_types6.default.bool,
    id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    max: import_prop_types6.default.oneOfType([import_prop_types6.default.instanceOf(Date), import_prop_types6.default.number]),
    min: import_prop_types6.default.oneOfType([import_prop_types6.default.instanceOf(Date), import_prop_types6.default.number]),
    position: import_prop_types6.default.oneOf(["bottom", "left", "right", "top"]),
    reverse: import_prop_types6.default.bool,
    scaleType: import_prop_types6.default.oneOf(["band", "linear", "log", "point", "pow", "sqrt", "time", "utc"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number,
    valueFormatter: import_prop_types6.default.func
  })),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.
   */
  yAxis: import_prop_types6.default.arrayOf(import_prop_types6.default.shape({
    axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    classes: import_prop_types6.default.object,
    data: import_prop_types6.default.array,
    dataKey: import_prop_types6.default.string,
    disableLine: import_prop_types6.default.bool,
    disableTicks: import_prop_types6.default.bool,
    fill: import_prop_types6.default.string,
    hideTooltip: import_prop_types6.default.bool,
    id: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
    label: import_prop_types6.default.string,
    labelFontSize: import_prop_types6.default.number,
    labelStyle: import_prop_types6.default.object,
    max: import_prop_types6.default.oneOfType([import_prop_types6.default.instanceOf(Date), import_prop_types6.default.number]),
    min: import_prop_types6.default.oneOfType([import_prop_types6.default.instanceOf(Date), import_prop_types6.default.number]),
    position: import_prop_types6.default.oneOf(["bottom", "left", "right", "top"]),
    reverse: import_prop_types6.default.bool,
    scaleType: import_prop_types6.default.oneOf(["band", "linear", "log", "point", "pow", "sqrt", "time", "utc"]),
    slotProps: import_prop_types6.default.object,
    slots: import_prop_types6.default.object,
    stroke: import_prop_types6.default.string,
    tickFontSize: import_prop_types6.default.number,
    tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
    tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
    tickLabelStyle: import_prop_types6.default.object,
    tickMaxStep: import_prop_types6.default.number,
    tickMinStep: import_prop_types6.default.number,
    tickNumber: import_prop_types6.default.number,
    tickSize: import_prop_types6.default.number,
    valueFormatter: import_prop_types6.default.func
  }))
} : void 0;

export {
  getPieArcUtilityClass,
  pieArcClasses,
  PieArc,
  PieArcPlot,
  getPieArcLabelUtilityClass,
  pieArcLabelClasses,
  PieArcLabel,
  PieArcLabelPlot,
  PiePlot,
  PieChart
};
//# sourceMappingURL=chunk-OOYESW5T.js.map
