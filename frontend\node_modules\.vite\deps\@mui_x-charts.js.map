{"version": 3, "sources": ["../../@mui/x-charts/esm/hooks/useDrawingArea.js", "../../@mui/x-charts/esm/ChartsReferenceLine/ChartsReferenceLine.js", "../../@mui/x-charts/esm/ChartsReferenceLine/ChartsXReferenceLine.js", "../../@mui/x-charts/esm/ChartsReferenceLine/common.js", "../../@mui/x-charts/esm/ChartsReferenceLine/chartsReferenceLineClasses.js", "../../@mui/x-charts/esm/ChartsReferenceLine/ChartsYReferenceLine.js", "../../@mui/x-charts/esm/BarChart/BarChart.js", "../../@mui/x-charts/esm/BarChart/BarPlot.js", "../../@mui/x-charts/esm/BarChart/BarElement.js", "../../@mui/x-charts/esm/ScatterChart/ScatterChart.js", "../../@mui/x-charts/esm/ScatterChart/ScatterPlot.js", "../../@mui/x-charts/esm/ScatterChart/Scatter.js", "../../@mui/x-charts/esm/SparkLineChart/SparkLineChart.js"], "sourcesContent": ["import * as React from 'react';\nimport { DrawingContext } from '../context/DrawingProvider';\nexport function useDrawingArea() {\n  const {\n    left,\n    top,\n    width,\n    height,\n    bottom,\n    right\n  } = React.useContext(DrawingContext);\n  return React.useMemo(() => ({\n    left,\n    top,\n    width,\n    height,\n    bottom,\n    right\n  }), [height, left, top, width, bottom, right]);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ChartsXReferenceLine } from './ChartsXReferenceLine';\nimport { ChartsYReferenceLine } from './ChartsYReferenceLine';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction ChartsReferenceLine(props) {\n  const {\n    x,\n    y\n  } = props;\n  if (x !== undefined && y !== undefined) {\n    throw new Error('MUI-X-Charts: The ChartsReferenceLine can not have both `x` and `y` props set.');\n  }\n  if (x === undefined && y === undefined) {\n    throw new Error('MUI-X-Charts: The ChartsReferenceLine should have a value in `x` or `y` prop.');\n  }\n  if (x !== undefined) {\n    return /*#__PURE__*/_jsx(ChartsXReferenceLine, _extends({}, props));\n  }\n  return /*#__PURE__*/_jsx(ChartsYReferenceLine, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsReferenceLine.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The id of the axis used for the reference value.\n   * @default The `id` of the first defined axis.\n   */\n  axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The label to display along the reference line.\n   */\n  label: PropTypes.string,\n  /**\n   * The alignment if the label is in the chart drawing area.\n   * @default 'middle'\n   */\n  labelAlign: PropTypes.oneOf(['end', 'middle', 'start']),\n  /**\n   * The style applied to the label.\n   */\n  labelStyle: PropTypes.object,\n  /**\n   * The style applied to the line.\n   */\n  lineStyle: PropTypes.object,\n  /**\n   * Additional space arround the label in px.\n   * Can be a number or an object `{ x, y }` to distinguish space with the reference line and space with axes.\n   * @default 5\n   */\n  spacing: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    x: PropTypes.number,\n    y: PropTypes.number\n  })]),\n  /**\n   * The x value associated with the reference line.\n   * If defined the reference line will be vertical.\n   */\n  x: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]),\n  /**\n   * The y value associated with the reference line.\n   * If defined the reference line will be horizontal.\n   */\n  y: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string])\n} : void 0;\nexport { ChartsReferenceLine };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDrawingArea, useXScale } from '../hooks';\nimport { ReferenceLineRoot } from './common';\nimport { ChartsText } from '../ChartsText';\nimport { getReferenceLineUtilityClass } from './chartsReferenceLineClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst getTextParams = ({\n  top,\n  height,\n  spacingY,\n  labelAlign = 'middle'\n}) => {\n  switch (labelAlign) {\n    case 'start':\n      return {\n        y: top + spacingY,\n        style: {\n          dominantBaseline: 'hanging',\n          textAnchor: 'start'\n        }\n      };\n    case 'end':\n      return {\n        y: top + height - spacingY,\n        style: {\n          dominantBaseline: 'auto',\n          textAnchor: 'start'\n        }\n      };\n    default:\n      return {\n        y: top + height / 2,\n        style: {\n          dominantBaseline: 'central',\n          textAnchor: 'start'\n        }\n      };\n  }\n};\nexport function getXReferenceLineClasses(classes) {\n  return composeClasses({\n    root: ['root', 'vertical'],\n    line: ['line'],\n    label: ['label']\n  }, getReferenceLineUtilityClass, classes);\n}\nlet warnedOnce = false;\nfunction ChartsXReferenceLine(props) {\n  var _spacing$x, _spacing$y;\n  const {\n    x,\n    label = '',\n    spacing = 5,\n    classes: inClasses,\n    labelAlign,\n    lineStyle,\n    labelStyle,\n    axisId\n  } = props;\n  const {\n    top,\n    height\n  } = useDrawingArea();\n  const xAxisScale = useXScale(axisId);\n  const xPosition = xAxisScale(x);\n  if (xPosition === undefined) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!warnedOnce) {\n        warnedOnce = true;\n        console.error(`MUI X: the value ${x} does not exist in the data of x axis with id ${axisId}.`);\n      }\n    }\n    return null;\n  }\n  const d = `M ${xPosition} ${top} l 0 ${height}`;\n  const classes = getXReferenceLineClasses(inClasses);\n  const spacingX = typeof spacing === 'object' ? (_spacing$x = spacing.x) != null ? _spacing$x : 0 : spacing;\n  const spacingY = typeof spacing === 'object' ? (_spacing$y = spacing.y) != null ? _spacing$y : 0 : spacing;\n  const textParams = _extends({\n    x: xPosition + spacingX,\n    text: label,\n    fontSize: 12\n  }, getTextParams({\n    top,\n    height,\n    spacingY,\n    labelAlign\n  }), {\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(ReferenceLineRoot, {\n    className: classes.root,\n    children: [/*#__PURE__*/_jsx(\"path\", {\n      d: d,\n      className: classes.line,\n      style: lineStyle\n    }), /*#__PURE__*/_jsx(ChartsText, _extends({}, textParams, {\n      style: _extends({}, textParams.style, labelStyle)\n    }))]\n  });\n}\nexport { ChartsXReferenceLine };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { styled } from '@mui/material/styles';\nimport { referenceLineClasses } from './chartsReferenceLineClasses';\nexport const ReferenceLineRoot = styled('g')(({\n  theme\n}) => ({\n  [`& .${referenceLineClasses.line}`]: {\n    fill: 'none',\n    stroke: (theme.vars || theme).palette.text.primary,\n    shapeRendering: 'crispEdges',\n    strokeWidth: 1,\n    pointerEvents: 'none'\n  },\n  [`& .${referenceLineClasses.label}`]: _extends({\n    fill: (theme.vars || theme).palette.text.primary,\n    stroke: 'none',\n    pointerEvents: 'none',\n    fontSize: 12\n  }, theme.typography.body1)\n}));", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getReferenceLineUtilityClass(slot) {\n  return generateUtilityClass('MuiChartsReferenceLine', slot);\n}\nexport const referenceLineClasses = generateUtilityClasses('MuiChartsReferenceLine', ['root', 'vertical', 'horizontal', 'line', 'label']);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDrawingArea, useYScale } from '../hooks';\nimport { ReferenceLineRoot } from './common';\nimport { ChartsText } from '../ChartsText';\nimport { getReferenceLineUtilityClass } from './chartsReferenceLineClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst getTextParams = ({\n  left,\n  width,\n  spacingX,\n  labelAlign = 'middle'\n}) => {\n  switch (labelAlign) {\n    case 'start':\n      return {\n        x: left + spacingX,\n        style: {\n          dominantBaseline: 'auto',\n          textAnchor: 'start'\n        }\n      };\n    case 'end':\n      return {\n        x: left + width - spacingX,\n        style: {\n          dominantBaseline: 'auto',\n          textAnchor: 'end'\n        }\n      };\n    default:\n      return {\n        x: left + width / 2,\n        style: {\n          dominantBaseline: 'auto',\n          textAnchor: 'middle'\n        }\n      };\n  }\n};\nlet warnedOnce = false;\nexport function getYReferenceLineClasses(classes) {\n  return composeClasses({\n    root: ['root', 'horizontal'],\n    line: ['line'],\n    label: ['label']\n  }, getReferenceLineUtilityClass, classes);\n}\nfunction ChartsYReferenceLine(props) {\n  var _spacing$x, _spacing$y;\n  const {\n    y,\n    label = '',\n    spacing = 5,\n    classes: inClasses,\n    labelAlign,\n    lineStyle,\n    labelStyle,\n    axisId\n  } = props;\n  const {\n    left,\n    width\n  } = useDrawingArea();\n  const yAxisScale = useYScale(axisId);\n  const yPosition = yAxisScale(y);\n  if (yPosition === undefined) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!warnedOnce) {\n        warnedOnce = true;\n        console.error(`MUI X: the value ${y} does not exist in the data of y axis with id ${axisId}.`);\n      }\n    }\n    return null;\n  }\n  const d = `M ${left} ${yPosition} l ${width} 0`;\n  const classes = getYReferenceLineClasses(inClasses);\n  const spacingX = typeof spacing === 'object' ? (_spacing$x = spacing.x) != null ? _spacing$x : 0 : spacing;\n  const spacingY = typeof spacing === 'object' ? (_spacing$y = spacing.y) != null ? _spacing$y : 0 : spacing;\n  const textParams = _extends({\n    y: yPosition - spacingY,\n    text: label,\n    fontSize: 12\n  }, getTextParams({\n    left,\n    width,\n    spacingX,\n    labelAlign\n  }), {\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(ReferenceLineRoot, {\n    className: classes.root,\n    children: [/*#__PURE__*/_jsx(\"path\", {\n      d: d,\n      className: classes.line,\n      style: lineStyle\n    }), /*#__PURE__*/_jsx(ChartsText, _extends({}, textParams, {\n      style: _extends({}, textParams.style, labelStyle)\n    }))]\n  });\n}\nexport { ChartsYReferenceLine };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useId from '@mui/utils/useId';\nimport PropTypes from 'prop-types';\nimport { BarPlot } from './BarPlot';\nimport { ResponsiveChartContainer } from '../ResponsiveChartContainer';\nimport { ChartsAxis } from '../ChartsAxis';\nimport { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from '../constants';\nimport { ChartsTooltip } from '../ChartsTooltip';\nimport { ChartsLegend } from '../ChartsLegend';\nimport { ChartsAxisHighlight } from '../ChartsAxisHighlight';\nimport { ChartsClipPath } from '../ChartsClipPath';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Bars](https://mui.com/x/react-charts/bars/)\n * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)\n * - [Stacking](https://mui.com/x/react-charts/stacking/)\n *\n * API:\n *\n * - [BarChart API](https://mui.com/x/api/charts/bar-chart/)\n */\nconst BarChart = /*#__PURE__*/React.forwardRef(function BarChart(props, ref) {\n  const {\n    xAxis,\n    yAxis,\n    series,\n    width,\n    height,\n    margin,\n    colors,\n    dataset,\n    sx,\n    layout,\n    tooltip,\n    axisHighlight,\n    legend,\n    topAxis,\n    leftAxis,\n    rightAxis,\n    bottomAxis,\n    skipAnimation,\n    children,\n    slots,\n    slotProps\n  } = props;\n  const id = useId();\n  const clipPathId = `${id}-clip-path`;\n  const hasHorizontalSeries = layout === 'horizontal' || layout === undefined && series.some(item => item.layout === 'horizontal');\n  const defaultAxisConfig = {\n    scaleType: 'band',\n    data: Array.from({\n      length: Math.max(...series.map(s => {\n        var _ref, _s$data;\n        return ((_ref = (_s$data = s.data) != null ? _s$data : dataset) != null ? _ref : []).length;\n      }))\n    }, (_, index) => index)\n  };\n  const defaultizedAxisHighlight = _extends({}, hasHorizontalSeries ? {\n    y: 'band'\n  } : {\n    x: 'band'\n  }, axisHighlight);\n  return /*#__PURE__*/_jsxs(ResponsiveChartContainer, {\n    ref: ref,\n    series: series.map(s => _extends({\n      type: 'bar'\n    }, s, {\n      layout: hasHorizontalSeries ? 'horizontal' : 'vertical'\n    })),\n    width: width,\n    height: height,\n    margin: margin,\n    xAxis: xAxis != null ? xAxis : hasHorizontalSeries ? undefined : [_extends({\n      id: DEFAULT_X_AXIS_KEY\n    }, defaultAxisConfig)],\n    yAxis: yAxis != null ? yAxis : hasHorizontalSeries ? [_extends({\n      id: DEFAULT_Y_AXIS_KEY\n    }, defaultAxisConfig)] : undefined,\n    colors: colors,\n    dataset: dataset,\n    sx: sx,\n    disableAxisListener: (tooltip == null ? void 0 : tooltip.trigger) !== 'axis' && (axisHighlight == null ? void 0 : axisHighlight.x) === 'none' && (axisHighlight == null ? void 0 : axisHighlight.y) === 'none',\n    children: [/*#__PURE__*/_jsx(\"g\", {\n      clipPath: `url(#${clipPathId})`,\n      children: /*#__PURE__*/_jsx(BarPlot, {\n        slots: slots,\n        slotProps: slotProps,\n        skipAnimation: skipAnimation\n      })\n    }), /*#__PURE__*/_jsx(ChartsAxis, {\n      topAxis: topAxis,\n      leftAxis: leftAxis,\n      rightAxis: rightAxis,\n      bottomAxis: bottomAxis,\n      slots: slots,\n      slotProps: slotProps\n    }), /*#__PURE__*/_jsx(ChartsLegend, _extends({}, legend, {\n      slots: slots,\n      slotProps: slotProps\n    })), /*#__PURE__*/_jsx(ChartsAxisHighlight, _extends({}, defaultizedAxisHighlight)), /*#__PURE__*/_jsx(ChartsTooltip, _extends({}, tooltip, {\n      slots: slots,\n      slotProps: slotProps\n    })), /*#__PURE__*/_jsx(ChartsClipPath, {\n      id: clipPathId\n    }), children]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? BarChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Object `{ x, y }` that defines how the charts highlight the mouse position along the x- and y-axes.\n   * The two properties accept the following values:\n   * - 'none': display nothing.\n   * - 'line': display a line at the current mouse position.\n   * - 'band': display a band at the current mouse position. Only available with band scale.\n   */\n  axisHighlight: PropTypes.shape({\n    x: PropTypes.oneOf(['band', 'line', 'none']),\n    y: PropTypes.oneOf(['band', 'line', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the bottom of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default xAxisIds[0] The id of the first provided axis\n   */\n  bottomAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default blueberryTwilightPalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * If `true`, the charts will not listen to the mouse move event.\n   * It might break interactive features, but will improve performance.\n   * @default false\n   */\n  disableAxisListener: PropTypes.bool,\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   * @default undefined\n   */\n  height: PropTypes.number,\n  layout: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * Indicate which axis to display the left of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default yAxisIds[0] The id of the first provided axis\n   */\n  leftAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  /**\n   * @deprecated Consider using `slotProps.legend` instead.\n   */\n  legend: PropTypes.shape({\n    classes: PropTypes.object,\n    direction: PropTypes.oneOf(['column', 'row']),\n    hidden: PropTypes.bool,\n    position: PropTypes.shape({\n      horizontal: PropTypes.oneOf(['left', 'middle', 'right']).isRequired,\n      vertical: PropTypes.oneOf(['bottom', 'middle', 'top']).isRequired\n    }),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object\n  }),\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   * @default object Depends on the charts type.\n   */\n  margin: PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  }),\n  /**\n   * Indicate which axis to display the right of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default null\n   */\n  rightAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  series: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string,\n    data: PropTypes.arrayOf(PropTypes.number),\n    dataKey: PropTypes.string,\n    highlightScope: PropTypes.shape({\n      faded: PropTypes.oneOf(['global', 'none', 'series']),\n      highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n    }),\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    layout: PropTypes.oneOf(['horizontal', 'vertical']),\n    stack: PropTypes.string,\n    stackOffset: PropTypes.oneOf(['diverging', 'expand', 'none', 'silhouette', 'wiggle']),\n    stackOrder: PropTypes.oneOf(['appearance', 'ascending', 'descending', 'insideOut', 'none', 'reverse']),\n    type: PropTypes.oneOf(['bar']),\n    valueFormatter: PropTypes.func,\n    xAxisKey: PropTypes.string,\n    yAxisKey: PropTypes.string\n  })).isRequired,\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  title: PropTypes.string,\n  tooltip: PropTypes.shape({\n    axisContent: PropTypes.elementType,\n    classes: PropTypes.object,\n    itemContent: PropTypes.elementType,\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    trigger: PropTypes.oneOf(['axis', 'item', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the top of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default null\n   */\n  topAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  viewBox: PropTypes.shape({\n    height: PropTypes.number,\n    width: PropTypes.number,\n    x: PropTypes.number,\n    y: PropTypes.number\n  }),\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   * @default undefined\n   */\n  width: PropTypes.number,\n  /**\n   * The configuration of the x-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.\n   */\n  xAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  })),\n  /**\n   * The configuration of the y-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.\n   */\n  yAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }))\n} : void 0;\nexport { BarChart };", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"skipAnimation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTransition } from '@react-spring/web';\nimport { SeriesContext } from '../context/SeriesContextProvider';\nimport { CartesianContext } from '../context/CartesianContextProvider';\nimport { BarElement } from './BarElement';\nimport { isBandScaleConfig } from '../models/axis';\nimport { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from '../constants';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Solution of the equations\n * W = barWidth * N + offset * (N-1)\n * offset / (offset + barWidth) = r\n * @param bandWidth The width available to place bars.\n * @param numberOfGroups The number of bars to place in that space.\n * @param gapRatio The ratio of the gap between bars over the bar width.\n * @returns The bar width and the offset between bars.\n */\nfunction getBandSize({\n  bandWidth: W,\n  numberOfGroups: N,\n  gapRatio: r\n}) {\n  if (r === 0) {\n    return {\n      barWidth: W / N,\n      offset: 0\n    };\n  }\n  const barWidth = W / (N + (N - 1) * r);\n  const offset = r * barWidth;\n  return {\n    barWidth,\n    offset\n  };\n}\nconst useCompletedData = () => {\n  var _React$useContext$bar;\n  const seriesData = (_React$useContext$bar = React.useContext(SeriesContext).bar) != null ? _React$useContext$bar : {\n    series: {},\n    stackingGroups: [],\n    seriesOrder: []\n  };\n  const axisData = React.useContext(CartesianContext);\n  const {\n    series,\n    stackingGroups\n  } = seriesData;\n  const {\n    xAxis,\n    yAxis,\n    xAxisIds,\n    yAxisIds\n  } = axisData;\n  const defaultXAxisId = xAxisIds[0];\n  const defaultYAxisId = yAxisIds[0];\n  const data = stackingGroups.flatMap(({\n    ids: groupIds\n  }, groupIndex) => {\n    return groupIds.flatMap(seriesId => {\n      var _series$seriesId$xAxi, _series$seriesId$yAxi;\n      const xAxisKey = (_series$seriesId$xAxi = series[seriesId].xAxisKey) != null ? _series$seriesId$xAxi : defaultXAxisId;\n      const yAxisKey = (_series$seriesId$yAxi = series[seriesId].yAxisKey) != null ? _series$seriesId$yAxi : defaultYAxisId;\n      const xAxisConfig = xAxis[xAxisKey];\n      const yAxisConfig = yAxis[yAxisKey];\n      const verticalLayout = series[seriesId].layout === 'vertical';\n      let baseScaleConfig;\n      if (verticalLayout) {\n        if (!isBandScaleConfig(xAxisConfig)) {\n          throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? 'The first `xAxis`' : `The x-axis with id \"${xAxisKey}\"`} shoud be of type \"band\" to display the bar series of id \"${seriesId}\"`);\n        }\n        if (xAxis[xAxisKey].data === undefined) {\n          throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? 'The first `xAxis`' : `The x-axis with id \"${xAxisKey}\"`} shoud have data property`);\n        }\n        baseScaleConfig = xAxisConfig;\n      } else {\n        if (!isBandScaleConfig(yAxisConfig)) {\n          throw new Error(`MUI-X-Charts: ${yAxisKey === DEFAULT_Y_AXIS_KEY ? 'The first `yAxis`' : `The y-axis with id \"${yAxisKey}\"`} shoud be of type \"band\" to display the bar series of id \"${seriesId}\"`);\n        }\n        if (yAxis[yAxisKey].data === undefined) {\n          throw new Error(`MUI-X-Charts: ${yAxisKey === DEFAULT_Y_AXIS_KEY ? 'The first `yAxis`' : `The y-axis with id \"${yAxisKey}\"`} shoud have data property`);\n        }\n        baseScaleConfig = yAxisConfig;\n      }\n      const xScale = xAxisConfig.scale;\n      const yScale = yAxisConfig.scale;\n      const bandWidth = baseScaleConfig.scale.bandwidth();\n      const {\n        barWidth,\n        offset\n      } = getBandSize({\n        bandWidth,\n        numberOfGroups: stackingGroups.length,\n        gapRatio: baseScaleConfig.barGapRatio\n      });\n      const barOffset = groupIndex * (barWidth + offset);\n      const {\n        stackedData,\n        color\n      } = series[seriesId];\n      return stackedData.map((values, dataIndex) => {\n        var _xAxis$xAxisKey$data, _yAxis$yAxisKey$data;\n        const valueCoordinates = values.map(v => verticalLayout ? yScale(v) : xScale(v));\n        const minValueCoord = Math.min(...valueCoordinates);\n        const maxValueCoord = Math.max(...valueCoordinates);\n        return {\n          seriesId,\n          dataIndex,\n          layout: series[seriesId].layout,\n          x: verticalLayout ? xScale((_xAxis$xAxisKey$data = xAxis[xAxisKey].data) == null ? void 0 : _xAxis$xAxisKey$data[dataIndex]) + barOffset : minValueCoord,\n          y: verticalLayout ? minValueCoord : yScale((_yAxis$yAxisKey$data = yAxis[yAxisKey].data) == null ? void 0 : _yAxis$yAxisKey$data[dataIndex]) + barOffset,\n          xOrigin: xScale(0),\n          yOrigin: yScale(0),\n          height: verticalLayout ? maxValueCoord - minValueCoord : barWidth,\n          width: verticalLayout ? barWidth : maxValueCoord - minValueCoord,\n          color,\n          highlightScope: series[seriesId].highlightScope\n        };\n      });\n    });\n  });\n  return data;\n};\nconst getOutStyle = ({\n  layout,\n  yOrigin,\n  x,\n  width,\n  y,\n  xOrigin,\n  height\n}) => _extends({}, layout === 'vertical' ? {\n  y: yOrigin,\n  x,\n  height: 0,\n  width\n} : {\n  y,\n  x: xOrigin,\n  height,\n  width: 0\n});\nconst getInStyle = ({\n  x,\n  width,\n  y,\n  height\n}) => ({\n  y,\n  x,\n  height,\n  width\n});\n\n/**\n * Demos:\n *\n * - [Bars](https://mui.com/x/react-charts/bars/)\n * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)\n * - [Stacking](https://mui.com/x/react-charts/stacking/)\n *\n * API:\n *\n * - [BarPlot API](https://mui.com/x/api/charts/bar-plot/)\n */\nfunction BarPlot(props) {\n  const completedData = useCompletedData();\n  const {\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const transition = useTransition(completedData, {\n    keys: bar => `${bar.seriesId}-${bar.dataIndex}`,\n    from: getOutStyle,\n    leave: getOutStyle,\n    enter: getInStyle,\n    update: getInStyle,\n    immediate: skipAnimation\n  });\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: transition((style, {\n      seriesId,\n      dataIndex,\n      color,\n      highlightScope\n    }) => /*#__PURE__*/_jsx(BarElement, _extends({\n      id: seriesId,\n      dataIndex: dataIndex,\n      highlightScope: highlightScope,\n      color: color\n    }, other, {\n      style: style\n    })))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? BarPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { BarPlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"dataIndex\", \"classes\", \"color\", \"highlightScope\", \"slots\", \"slotProps\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useSlotProps } from '@mui/base/utils';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { styled } from '@mui/material/styles';\nimport { color as d3Color } from 'd3-color';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport { animated } from '@react-spring/web';\nimport { getIsFaded, getIsHighlighted, useInteractionItemProps } from '../hooks/useInteractionItemProps';\nimport { InteractionContext } from '../context/InteractionProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getBarElementUtilityClass(slot) {\n  return generateUtilityClass('MuiBarElement', slot);\n}\nexport const barElementClasses = generateUtilityClasses('MuiBarElement', ['root']);\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`]\n  };\n  return composeClasses(slots, getBarElementUtilityClass, classes);\n};\nexport const BarElementPath = styled(animated.rect, {\n  name: 'MuiBarElement',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  ownerState\n}) => ({\n  stroke: 'none',\n  shapeRendering: 'crispEdges',\n  fill: ownerState.isHighlighted ? d3Color(ownerState.color).brighter(0.5).formatHex() : ownerState.color,\n  transition: 'opacity 0.2s ease-in, fill 0.2s ease-in',\n  opacity: ownerState.isFaded && 0.3 || 1\n}));\nfunction BarElement(props) {\n  var _slots$bar;\n  const {\n      id,\n      dataIndex,\n      classes: innerClasses,\n      color,\n      highlightScope,\n      slots,\n      slotProps,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const getInteractionItemProps = useInteractionItemProps(highlightScope);\n  const {\n    item\n  } = React.useContext(InteractionContext);\n  const isHighlighted = getIsHighlighted(item, {\n    type: 'bar',\n    seriesId: id,\n    dataIndex\n  }, highlightScope);\n  const isFaded = !isHighlighted && getIsFaded(item, {\n    type: 'bar',\n    seriesId: id,\n    dataIndex\n  }, highlightScope);\n  const ownerState = {\n    id,\n    dataIndex,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted\n  };\n  const classes = useUtilityClasses(ownerState);\n  const Bar = (_slots$bar = slots == null ? void 0 : slots.bar) != null ? _slots$bar : BarElementPath;\n  const barProps = useSlotProps({\n    elementType: Bar,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.bar,\n    additionalProps: _extends({}, other, getInteractionItemProps({\n      type: 'bar',\n      seriesId: id,\n      dataIndex\n    }), {\n      style,\n      className: classes.root\n    }),\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Bar, _extends({}, barProps));\n}\nprocess.env.NODE_ENV !== \"production\" ? BarElement.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  dataIndex: PropTypes.number.isRequired,\n  highlightScope: PropTypes.shape({\n    faded: PropTypes.oneOf(['global', 'none', 'series']),\n    highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n  }),\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { BarElement };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ScatterPlot } from './ScatterPlot';\nimport { ResponsiveChartContainer } from '../ResponsiveChartContainer';\nimport { ChartsAxis } from '../ChartsAxis';\nimport { ChartsTooltip } from '../ChartsTooltip';\nimport { ChartsLegend } from '../ChartsLegend';\nimport { ChartsAxisHighlight } from '../ChartsAxisHighlight';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Scatter](https://mui.com/x/react-charts/scatter/)\n * - [Scatter demonstration](https://mui.com/x/react-charts/scatter-demo/)\n *\n * API:\n *\n * - [ScatterChart API](https://mui.com/x/api/charts/scatter-chart/)\n */\nconst ScatterChart = /*#__PURE__*/React.forwardRef(function ScatterChart(props, ref) {\n  const {\n    xAxis,\n    yAxis,\n    series,\n    tooltip,\n    axisHighlight,\n    legend,\n    width,\n    height,\n    margin,\n    colors,\n    sx,\n    topAxis,\n    leftAxis,\n    rightAxis,\n    bottomAxis,\n    children,\n    slots,\n    slotProps\n  } = props;\n  return /*#__PURE__*/_jsxs(ResponsiveChartContainer, {\n    ref: ref,\n    series: series.map(s => _extends({\n      type: 'scatter'\n    }, s)),\n    width: width,\n    height: height,\n    margin: margin,\n    colors: colors,\n    xAxis: xAxis,\n    yAxis: yAxis,\n    sx: sx,\n    children: [/*#__PURE__*/_jsx(ChartsAxis, {\n      topAxis: topAxis,\n      leftAxis: leftAxis,\n      rightAxis: rightAxis,\n      bottomAxis: bottomAxis,\n      slots: slots,\n      slotProps: slotProps\n    }), /*#__PURE__*/_jsx(ScatterPlot, {\n      slots: slots,\n      slotProps: slotProps\n    }), /*#__PURE__*/_jsx(ChartsLegend, _extends({}, legend, {\n      slots: slots,\n      slotProps: slotProps\n    })), /*#__PURE__*/_jsx(ChartsAxisHighlight, _extends({\n      x: \"none\",\n      y: \"none\"\n    }, axisHighlight)), /*#__PURE__*/_jsx(ChartsTooltip, _extends({\n      trigger: \"item\"\n    }, tooltip)), children]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ScatterChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  axisHighlight: PropTypes.shape({\n    x: PropTypes.oneOf(['band', 'line', 'none']),\n    y: PropTypes.oneOf(['band', 'line', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the bottom of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default xAxisIds[0] The id of the first provided axis\n   */\n  bottomAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default blueberryTwilightPalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * If `true`, the charts will not listen to the mouse move event.\n   * It might break interactive features, but will improve performance.\n   * @default false\n   */\n  disableAxisListener: PropTypes.bool,\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   * @default undefined\n   */\n  height: PropTypes.number,\n  /**\n   * Indicate which axis to display the left of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default yAxisIds[0] The id of the first provided axis\n   */\n  leftAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  /**\n   * @deprecated Consider using `slotProps.legend` instead.\n   */\n  legend: PropTypes.shape({\n    classes: PropTypes.object,\n    direction: PropTypes.oneOf(['column', 'row']),\n    hidden: PropTypes.bool,\n    position: PropTypes.shape({\n      horizontal: PropTypes.oneOf(['left', 'middle', 'right']).isRequired,\n      vertical: PropTypes.oneOf(['bottom', 'middle', 'top']).isRequired\n    }),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object\n  }),\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   * @default object Depends on the charts type.\n   */\n  margin: PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  }),\n  /**\n   * Indicate which axis to display the right of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default null\n   */\n  rightAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  series: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string,\n    data: PropTypes.arrayOf(PropTypes.shape({\n      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n      x: PropTypes.number.isRequired,\n      y: PropTypes.number.isRequired\n    })).isRequired,\n    highlightScope: PropTypes.shape({\n      faded: PropTypes.oneOf(['global', 'none', 'series']),\n      highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n    }),\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    markerSize: PropTypes.number,\n    type: PropTypes.oneOf(['scatter']),\n    valueFormatter: PropTypes.func,\n    xAxisKey: PropTypes.string,\n    yAxisKey: PropTypes.string\n  })).isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  title: PropTypes.string,\n  tooltip: PropTypes.shape({\n    axisContent: PropTypes.elementType,\n    classes: PropTypes.object,\n    itemContent: PropTypes.elementType,\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    trigger: PropTypes.oneOf(['axis', 'item', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the top of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default null\n   */\n  topAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  viewBox: PropTypes.shape({\n    height: PropTypes.number,\n    width: PropTypes.number,\n    x: PropTypes.number,\n    y: PropTypes.number\n  }),\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   * @default undefined\n   */\n  width: PropTypes.number,\n  /**\n   * The configuration of the x-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.\n   */\n  xAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  })),\n  /**\n   * The configuration of the y-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.\n   */\n  yAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }))\n} : void 0;\nexport { ScatterChart };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Scatter } from './Scatter';\nimport { SeriesContext } from '../context/SeriesContextProvider';\nimport { CartesianContext } from '../context/CartesianContextProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Scatter](https://mui.com/x/react-charts/scatter/)\n * - [Scatter demonstration](https://mui.com/x/react-charts/scatter-demo/)\n *\n * API:\n *\n * - [ScatterPlot API](https://mui.com/x/api/charts/scatter-plot/)\n */\nfunction ScatterPlot(props) {\n  var _slots$scatter;\n  const {\n    slots,\n    slotProps\n  } = props;\n  const seriesData = React.useContext(SeriesContext).scatter;\n  const axisData = React.useContext(CartesianContext);\n  if (seriesData === undefined) {\n    return null;\n  }\n  const {\n    series,\n    seriesOrder\n  } = seriesData;\n  const {\n    xAxis,\n    yAxis,\n    xAxisIds,\n    yAxisIds\n  } = axisData;\n  const defaultXAxisId = xAxisIds[0];\n  const defaultYAxisId = yAxisIds[0];\n  const ScatterItems = (_slots$scatter = slots == null ? void 0 : slots.scatter) != null ? _slots$scatter : Scatter;\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: seriesOrder.map(seriesId => {\n      const {\n        id,\n        xAxisKey,\n        yAxisKey,\n        markerSize,\n        color\n      } = series[seriesId];\n      const xScale = xAxis[xAxisKey != null ? xAxisKey : defaultXAxisId].scale;\n      const yScale = yAxis[yAxisKey != null ? yAxisKey : defaultYAxisId].scale;\n      return /*#__PURE__*/_jsx(ScatterItems, _extends({\n        xScale: xScale,\n        yScale: yScale,\n        color: color,\n        markerSize: markerSize != null ? markerSize : 4,\n        series: series[seriesId]\n      }, slotProps == null ? void 0 : slotProps.scatter), id);\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ScatterPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { ScatterPlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getValueToPositionMapper } from '../hooks/useScale';\nimport { getIsFaded, getIsHighlighted, useInteractionItemProps } from '../hooks/useInteractionItemProps';\nimport { InteractionContext } from '../context/InteractionProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Scatter](https://mui.com/x/react-charts/scatter/)\n * - [Scatter demonstration](https://mui.com/x/react-charts/scatter-demo/)\n *\n * API:\n *\n * - [Scatter API](https://mui.com/x/api/charts/scatter/)\n */\nfunction Scatter(props) {\n  const {\n    series,\n    xScale,\n    yScale,\n    color,\n    markerSize\n  } = props;\n  const {\n    item\n  } = React.useContext(InteractionContext);\n  const getInteractionItemProps = useInteractionItemProps(series.highlightScope);\n  const cleanData = React.useMemo(() => {\n    const getXPosition = getValueToPositionMapper(xScale);\n    const getYPosition = getValueToPositionMapper(yScale);\n    const xRange = xScale.range();\n    const yRange = yScale.range();\n    const minXRange = Math.min(...xRange);\n    const maxXRange = Math.max(...xRange);\n    const minYRange = Math.min(...yRange);\n    const maxYRange = Math.max(...yRange);\n    const temp = [];\n    for (let i = 0; i < series.data.length; i += 1) {\n      const scatterPoint = series.data[i];\n      const x = getXPosition(scatterPoint.x);\n      const y = getYPosition(scatterPoint.y);\n      const isInRange = x >= minXRange && x <= maxXRange && y >= minYRange && y <= maxYRange;\n      const pointCtx = {\n        type: 'scatter',\n        seriesId: series.id,\n        dataIndex: i\n      };\n      if (isInRange) {\n        temp.push({\n          x,\n          y,\n          isFaded: !getIsHighlighted(item, pointCtx, series.highlightScope) && getIsFaded(item, pointCtx, series.highlightScope),\n          interactionProps: getInteractionItemProps(pointCtx),\n          id: scatterPoint.id\n        });\n      }\n    }\n    return temp;\n  }, [yScale, xScale, getInteractionItemProps, item, series.data, series.highlightScope, series.id]);\n  return /*#__PURE__*/_jsx(\"g\", {\n    children: cleanData.map(dataPoint => /*#__PURE__*/_jsx(\"circle\", _extends({\n      cx: 0,\n      cy: 0,\n      r: markerSize,\n      transform: `translate(${dataPoint.x}, ${dataPoint.y})`,\n      fill: color,\n      opacity: dataPoint.isFaded && 0.3 || 1\n    }, dataPoint.interactionProps), dataPoint.id))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Scatter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  color: PropTypes.string.isRequired,\n  markerSize: PropTypes.number.isRequired,\n  series: PropTypes.shape({\n    color: PropTypes.string.isRequired,\n    data: PropTypes.arrayOf(PropTypes.shape({\n      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n      x: PropTypes.number.isRequired,\n      y: PropTypes.number.isRequired\n    })).isRequired,\n    highlightScope: PropTypes.shape({\n      faded: PropTypes.oneOf(['global', 'none', 'series']),\n      highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n    }),\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n    label: PropTypes.string,\n    markerSize: PropTypes.number,\n    type: PropTypes.oneOf(['scatter']).isRequired,\n    valueFormatter: PropTypes.func.isRequired,\n    xAxisKey: PropTypes.string,\n    yAxisKey: PropTypes.string\n  }).isRequired,\n  xScale: PropTypes.func.isRequired,\n  yScale: PropTypes.func.isRequired\n} : void 0;\nexport { Scatter };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { BarPlot } from '../BarChart';\nimport { LinePlot, AreaPlot, LineHighlightPlot } from '../LineChart';\nimport { ResponsiveChartContainer } from '../ResponsiveChartContainer';\nimport { DEFAULT_X_AXIS_KEY } from '../constants';\nimport { ChartsTooltip } from '../ChartsTooltip';\nimport { ChartsAxisHighlight } from '../ChartsAxisHighlight';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPARKLINE_DEFAULT_MARGIN = {\n  top: 5,\n  bottom: 5,\n  left: 5,\n  right: 5\n};\n\n/**\n * Demos:\n *\n * - [SparkLine](https://mui.com/x/react-charts/sparkline/)\n *\n * API:\n *\n * - [SparkLineChart API](https://mui.com/x/api/charts/spark-line-chart/)\n */\nconst SparkLineChart = /*#__PURE__*/React.forwardRef(function SparkLineChart(props, ref) {\n  const {\n    xAxis,\n    width,\n    height,\n    margin = SPARKLINE_DEFAULT_MARGIN,\n    colors,\n    sx,\n    showTooltip,\n    tooltip,\n    showHighlight,\n    axisHighlight: inAxisHighlight,\n    children,\n    slots,\n    slotProps,\n    data,\n    plotType = 'line',\n    valueFormatter = value => value === null ? '' : value.toString(),\n    area,\n    curve = 'linear',\n    skipAnimation = false\n  } = props;\n  const defaultXHighlight = showHighlight && plotType === 'bar' ? {\n    x: 'band'\n  } : {\n    x: 'none'\n  };\n  const axisHighlight = _extends({}, defaultXHighlight, inAxisHighlight);\n  return /*#__PURE__*/_jsxs(ResponsiveChartContainer, {\n    ref: ref,\n    series: [_extends({\n      type: plotType,\n      data,\n      valueFormatter\n    }, plotType === 'bar' ? {} : {\n      area,\n      curve,\n      disableHighlight: !showHighlight\n    })],\n    width: width,\n    height: height,\n    margin: margin,\n    xAxis: [_extends({\n      id: DEFAULT_X_AXIS_KEY,\n      scaleType: plotType === 'bar' ? 'band' : 'point',\n      data: Array.from({\n        length: data.length\n      }, (_, index) => index),\n      hideTooltip: xAxis === undefined\n    }, xAxis)],\n    colors: colors,\n    sx: sx,\n    disableAxisListener: (!showTooltip || (tooltip == null ? void 0 : tooltip.trigger) !== 'axis') && (axisHighlight == null ? void 0 : axisHighlight.x) === 'none' && (axisHighlight == null ? void 0 : axisHighlight.y) === 'none',\n    children: [plotType === 'bar' && /*#__PURE__*/_jsx(BarPlot, {\n      skipAnimation: skipAnimation,\n      slots: slots,\n      slotProps: slotProps,\n      sx: {\n        shapeRendering: 'auto'\n      }\n    }), plotType === 'line' && /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(AreaPlot, {\n        slots: slots,\n        slotProps: slotProps\n      }), /*#__PURE__*/_jsx(LinePlot, {\n        slots: slots,\n        slotProps: slotProps\n      }), /*#__PURE__*/_jsx(LineHighlightPlot, {\n        slots: slots,\n        slotProps: slotProps\n      })]\n    }), /*#__PURE__*/_jsx(ChartsAxisHighlight, _extends({}, axisHighlight)), showTooltip && /*#__PURE__*/_jsx(ChartsTooltip, _extends({}, tooltip, {\n      slotProps: slotProps,\n      slots: slots\n    })), children]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SparkLineChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Set to `true` to fill spark line area.\n   * Has no effect if plotType='bar'.\n   * @default false\n   */\n  area: PropTypes.bool,\n  axisHighlight: PropTypes.shape({\n    x: PropTypes.oneOf(['band', 'line', 'none']),\n    y: PropTypes.oneOf(['band', 'line', 'none'])\n  }),\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default blueberryTwilightPalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * @default 'linear'\n   */\n  curve: PropTypes.oneOf(['catmullRom', 'linear', 'monotoneX', 'monotoneY', 'natural', 'step', 'stepAfter', 'stepBefore']),\n  /**\n   * Data to plot.\n   */\n  data: PropTypes.arrayOf(PropTypes.number).isRequired,\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * If `true`, the charts will not listen to the mouse move event.\n   * It might break interactive features, but will improve performance.\n   * @default false\n   */\n  disableAxisListener: PropTypes.bool,\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   * @default undefined\n   */\n  height: PropTypes.number,\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   * @default {\n   *   top: 5,\n   *   bottom: 5,\n   *   left: 5,\n   *   right: 5,\n   * }\n   */\n  margin: PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  }),\n  /**\n   * Type of plot used.\n   * @default 'line'\n   */\n  plotType: PropTypes.oneOf(['bar', 'line']),\n  /**\n   * Set to `true` to highlight the value.\n   * With line, it shows a point.\n   * With bar, it shows a highlight band.\n   * @default false\n   */\n  showHighlight: PropTypes.bool,\n  /**\n   * Set to `true` to enable the tooltip in the sparkline.\n   * @default false\n   */\n  showTooltip: PropTypes.bool,\n  /**\n   * If `true`, bar animations are skiped.\n   * @deprecated In v7 animations are skipped for sparkline.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  title: PropTypes.string,\n  tooltip: PropTypes.shape({\n    axisContent: PropTypes.elementType,\n    classes: PropTypes.object,\n    itemContent: PropTypes.elementType,\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    trigger: PropTypes.oneOf(['axis', 'item', 'none'])\n  }),\n  /**\n   * Formatter used by the tooltip.\n   * @param {number} value The value to format.\n   * @returns {string} the formatted value.\n   * @default (value: number | null) => (value === null ? '' : value.toString())\n   */\n  valueFormatter: PropTypes.func,\n  viewBox: PropTypes.shape({\n    height: PropTypes.number,\n    width: PropTypes.number,\n    x: PropTypes.number,\n    y: PropTypes.number\n  }),\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   * @default undefined\n   */\n  width: PropTypes.number,\n  /**\n   * The xAxis configuration.\n   * Notice it is a single configuration object, not an array of configuration.\n   */\n  xAxis: PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  })\n} : void 0;\nexport { SparkLineChart };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAEhB,SAAS,iBAAiB;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAU,iBAAW,cAAc;AACnC,SAAa,cAAQ,OAAO;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,QAAQ,MAAM,KAAK,OAAO,QAAQ,KAAK,CAAC;AAC/C;;;ACnBA;AACA,IAAAA,SAAuB;AACvB,wBAAsB;;;ACFtB;AACA,IAAAC,SAAuB;AACvB;;;ACFA;;;ACAA;AACA;AACO,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,0BAA0B,IAAI;AAC5D;AACO,IAAM,uBAAuB,uBAAuB,0BAA0B,CAAC,QAAQ,YAAY,cAAc,QAAQ,OAAO,CAAC;;;ADFjI,IAAM,oBAAoB,eAAO,GAAG,EAAE,CAAC;AAAA,EAC5C;AACF,OAAO;AAAA,EACL,CAAC,MAAM,qBAAqB,IAAI,EAAE,GAAG;AAAA,IACnC,MAAM;AAAA,IACN,SAAS,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC3C,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,EACjB;AAAA,EACA,CAAC,MAAM,qBAAqB,KAAK,EAAE,GAAG,SAAS;AAAA,IAC7C,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACzC,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU;AAAA,EACZ,GAAG,MAAM,WAAW,KAAK;AAC3B,EAAE;;;ADZF,yBAA4B;AAC5B,IAAAC,sBAA8B;AAC9B,IAAM,gBAAgB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AACf,MAAM;AACJ,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,GAAG,MAAM,SAAS;AAAA,QAClB,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACE,aAAO;AAAA,QACL,GAAG,MAAM,SAAS;AAAA,QAClB,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,EACJ;AACF;AACO,SAAS,yBAAyB,SAAS;AAChD,SAAO,eAAe;AAAA,IACpB,MAAM,CAAC,QAAQ,UAAU;AAAA,IACzB,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,OAAO;AAAA,EACjB,GAAG,8BAA8B,OAAO;AAC1C;AACA,IAAI,aAAa;AACjB,SAAS,qBAAqB,OAAO;AACnC,MAAI,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,aAAa,UAAU,MAAM;AACnC,QAAM,YAAY,WAAW,CAAC;AAC9B,MAAI,cAAc,QAAW;AAC3B,QAAI,MAAuC;AACzC,UAAI,CAAC,YAAY;AACf,qBAAa;AACb,gBAAQ,MAAM,oBAAoB,CAAC,iDAAiD,MAAM,GAAG;AAAA,MAC/F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,IAAI,KAAK,SAAS,IAAI,GAAG,QAAQ,MAAM;AAC7C,QAAM,UAAU,yBAAyB,SAAS;AAClD,QAAM,WAAW,OAAO,YAAY,YAAY,aAAa,QAAQ,MAAM,OAAO,aAAa,IAAI;AACnG,QAAM,WAAW,OAAO,YAAY,YAAY,aAAa,QAAQ,MAAM,OAAO,aAAa,IAAI;AACnG,QAAM,aAAa,SAAS;AAAA,IAC1B,GAAG,YAAY;AAAA,IACf,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,GAAG,cAAc;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG;AAAA,IACF,WAAW,QAAQ;AAAA,EACrB,CAAC;AACD,aAAoB,oBAAAC,MAAM,mBAAmB;AAAA,IAC3C,WAAW,QAAQ;AAAA,IACnB,UAAU,KAAc,mBAAAC,KAAK,QAAQ;AAAA,MACnC;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,OAAO;AAAA,IACT,CAAC,OAAgB,mBAAAA,KAAK,YAAY,SAAS,CAAC,GAAG,YAAY;AAAA,MACzD,OAAO,SAAS,CAAC,GAAG,WAAW,OAAO,UAAU;AAAA,IAClD,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;;;AGvGA;AACA,IAAAC,SAAuB;AACvB;AAKA,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAC9B,IAAMC,iBAAgB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AACf,MAAM;AACJ,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,QACL,GAAG,OAAO;AAAA,QACV,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,GAAG,OAAO,QAAQ;AAAA,QAClB,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACE,aAAO;AAAA,QACL,GAAG,OAAO,QAAQ;AAAA,QAClB,OAAO;AAAA,UACL,kBAAkB;AAAA,UAClB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,EACJ;AACF;AACA,IAAIC,cAAa;AACV,SAAS,yBAAyB,SAAS;AAChD,SAAO,eAAe;AAAA,IACpB,MAAM,CAAC,QAAQ,YAAY;AAAA,IAC3B,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,OAAO;AAAA,EACjB,GAAG,8BAA8B,OAAO;AAC1C;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,aAAa,UAAU,MAAM;AACnC,QAAM,YAAY,WAAW,CAAC;AAC9B,MAAI,cAAc,QAAW;AAC3B,QAAI,MAAuC;AACzC,UAAI,CAACA,aAAY;AACf,QAAAA,cAAa;AACb,gBAAQ,MAAM,oBAAoB,CAAC,iDAAiD,MAAM,GAAG;AAAA,MAC/F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,IAAI,KAAK,IAAI,IAAI,SAAS,MAAM,KAAK;AAC3C,QAAM,UAAU,yBAAyB,SAAS;AAClD,QAAM,WAAW,OAAO,YAAY,YAAY,aAAa,QAAQ,MAAM,OAAO,aAAa,IAAI;AACnG,QAAM,WAAW,OAAO,YAAY,YAAY,aAAa,QAAQ,MAAM,OAAO,aAAa,IAAI;AACnG,QAAM,aAAa,SAAS;AAAA,IAC1B,GAAG,YAAY;AAAA,IACf,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,GAAGD,eAAc;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG;AAAA,IACF,WAAW,QAAQ;AAAA,EACrB,CAAC;AACD,aAAoB,oBAAAE,MAAM,mBAAmB;AAAA,IAC3C,WAAW,QAAQ;AAAA,IACnB,UAAU,KAAc,oBAAAC,KAAK,QAAQ;AAAA,MACnC;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,OAAO;AAAA,IACT,CAAC,OAAgB,oBAAAA,KAAK,YAAY,SAAS,CAAC,GAAG,YAAY;AAAA,MACzD,OAAO,SAAS,CAAC,GAAG,WAAW,OAAO,UAAU;AAAA,IAClD,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;;;AJlGA,IAAAC,sBAA4B;AAC5B,SAAS,oBAAoB,OAAO;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAM,UAAa,MAAM,QAAW;AACtC,UAAM,IAAI,MAAM,gFAAgF;AAAA,EAClG;AACA,MAAI,MAAM,UAAa,MAAM,QAAW;AACtC,UAAM,IAAI,MAAM,+EAA+E;AAAA,EACjG;AACA,MAAI,MAAM,QAAW;AACnB,eAAoB,oBAAAC,KAAK,sBAAsB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,EACpE;AACA,aAAoB,oBAAAA,KAAK,sBAAsB,SAAS,CAAC,GAAG,KAAK,CAAC;AACpE;AACA,OAAwC,oBAAoB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStE,QAAQ,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhE,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,YAAY,kBAAAA,QAAU,MAAM,CAAC,OAAO,UAAU,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAItD,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IAC9D,GAAG,kBAAAA,QAAU;AAAA,IACb,GAAG,kBAAAA,QAAU;AAAA,EACf,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKH,GAAG,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,WAAW,IAAI,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvF,GAAG,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,WAAW,IAAI,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AACzF,IAAI;;;AKxEJ;AACA,IAAAC,SAAuB;AACvB;AACA,IAAAC,qBAAsB;;;ACHtB;AACA;AAEA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACJtB;AACA;AAEA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AACtB;AAEA;AAGA;AAIA,IAAAC,sBAA4B;AAZ5B,IAAM,YAAY,CAAC,MAAM,aAAa,WAAW,SAAS,kBAAkB,SAAS,aAAa,OAAO;AAalG,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACO,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,MAAM,CAAC;AACjF,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,EAAE;AAAA,EAC/B;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACO,IAAM,iBAAiB,eAAO,SAAS,MAAM;AAAA,EAClD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,GAAG,WAAW,OAAO;AAC3C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,MAAM,WAAW,gBAAgB,MAAQ,WAAW,KAAK,EAAE,SAAS,GAAG,EAAE,UAAU,IAAI,WAAW;AAAA,EAClG,YAAY;AAAA,EACZ,SAAS,WAAW,WAAW,OAAO;AACxC,EAAE;AACF,SAAS,WAAW,OAAO;AACzB,MAAI;AACJ,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,OAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,0BAA0B,wBAAwB,cAAc;AACtE,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,kBAAW,kBAAkB;AACvC,QAAM,gBAAgB,iBAAiB,MAAM;AAAA,IAC3C,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF,GAAG,cAAc;AACjB,QAAM,UAAU,CAAC,iBAAiB,WAAW,MAAM;AAAA,IACjD,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF,GAAG,cAAc;AACjB,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,OAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,OAAO,aAAa,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,aAAa;AACrF,QAAM,WAAW,aAAa;AAAA,IAC5B,aAAa;AAAA,IACb,mBAAmB,aAAa,OAAO,SAAS,UAAU;AAAA,IAC1D,iBAAiB,SAAS,CAAC,GAAG,OAAO,wBAAwB;AAAA,MAC3D,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,IACF,CAAC,GAAG;AAAA,MACF;AAAA,MACA,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,IACD;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAC,KAAK,KAAK,SAAS,CAAC,GAAG,QAAQ,CAAC;AACtD;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,SAAS,mBAAAC,QAAU;AAAA,EACnB,WAAW,mBAAAA,QAAU,OAAO;AAAA,EAC5B,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,IAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,IACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzD,CAAC;AAAA,EACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ADzGJ,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,eAAe;AAmBlC,SAAS,YAAY;AAAA,EACnB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,UAAU;AACZ,GAAG;AACD,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,MACL,UAAU,IAAI;AAAA,MACd,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,WAAW,KAAK,KAAK,IAAI,KAAK;AACpC,QAAM,SAAS,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,mBAAmB,MAAM;AAC7B,MAAI;AACJ,QAAM,cAAc,wBAA8B,kBAAW,aAAa,EAAE,QAAQ,OAAO,wBAAwB;AAAA,IACjH,QAAQ,CAAC;AAAA,IACT,gBAAgB,CAAC;AAAA,IACjB,aAAa,CAAC;AAAA,EAChB;AACA,QAAM,WAAiB,kBAAW,gBAAgB;AAClD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,SAAS,CAAC;AACjC,QAAM,iBAAiB,SAAS,CAAC;AACjC,QAAM,OAAO,eAAe,QAAQ,CAAC;AAAA,IACnC,KAAK;AAAA,EACP,GAAG,eAAe;AAChB,WAAO,SAAS,QAAQ,cAAY;AAClC,UAAI,uBAAuB;AAC3B,YAAM,YAAY,wBAAwB,OAAO,QAAQ,EAAE,aAAa,OAAO,wBAAwB;AACvG,YAAM,YAAY,wBAAwB,OAAO,QAAQ,EAAE,aAAa,OAAO,wBAAwB;AACvG,YAAM,cAAc,MAAM,QAAQ;AAClC,YAAM,cAAc,MAAM,QAAQ;AAClC,YAAM,iBAAiB,OAAO,QAAQ,EAAE,WAAW;AACnD,UAAI;AACJ,UAAI,gBAAgB;AAClB,YAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,gBAAM,IAAI,MAAM,iBAAiB,aAAa,qBAAqB,sBAAsB,uBAAuB,QAAQ,GAAG,6DAA6D,QAAQ,GAAG;AAAA,QACrM;AACA,YAAI,MAAM,QAAQ,EAAE,SAAS,QAAW;AACtC,gBAAM,IAAI,MAAM,iBAAiB,aAAa,qBAAqB,sBAAsB,uBAAuB,QAAQ,GAAG,2BAA2B;AAAA,QACxJ;AACA,0BAAkB;AAAA,MACpB,OAAO;AACL,YAAI,CAAC,kBAAkB,WAAW,GAAG;AACnC,gBAAM,IAAI,MAAM,iBAAiB,aAAa,qBAAqB,sBAAsB,uBAAuB,QAAQ,GAAG,6DAA6D,QAAQ,GAAG;AAAA,QACrM;AACA,YAAI,MAAM,QAAQ,EAAE,SAAS,QAAW;AACtC,gBAAM,IAAI,MAAM,iBAAiB,aAAa,qBAAqB,sBAAsB,uBAAuB,QAAQ,GAAG,2BAA2B;AAAA,QACxJ;AACA,0BAAkB;AAAA,MACpB;AACA,YAAM,SAAS,YAAY;AAC3B,YAAM,SAAS,YAAY;AAC3B,YAAM,YAAY,gBAAgB,MAAM,UAAU;AAClD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,YAAY;AAAA,QACd;AAAA,QACA,gBAAgB,eAAe;AAAA,QAC/B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,YAAM,YAAY,cAAc,WAAW;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA,OAAAC;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,aAAO,YAAY,IAAI,CAAC,QAAQ,cAAc;AAC5C,YAAI,sBAAsB;AAC1B,cAAM,mBAAmB,OAAO,IAAI,OAAK,iBAAiB,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AAC/E,cAAM,gBAAgB,KAAK,IAAI,GAAG,gBAAgB;AAClD,cAAM,gBAAgB,KAAK,IAAI,GAAG,gBAAgB;AAClD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,OAAO,QAAQ,EAAE;AAAA,UACzB,GAAG,iBAAiB,QAAQ,uBAAuB,MAAM,QAAQ,EAAE,SAAS,OAAO,SAAS,qBAAqB,SAAS,CAAC,IAAI,YAAY;AAAA,UAC3I,GAAG,iBAAiB,gBAAgB,QAAQ,uBAAuB,MAAM,QAAQ,EAAE,SAAS,OAAO,SAAS,qBAAqB,SAAS,CAAC,IAAI;AAAA,UAC/I,SAAS,OAAO,CAAC;AAAA,UACjB,SAAS,OAAO,CAAC;AAAA,UACjB,QAAQ,iBAAiB,gBAAgB,gBAAgB;AAAA,UACzD,OAAO,iBAAiB,WAAW,gBAAgB;AAAA,UACnD,OAAAA;AAAA,UACA,gBAAgB,OAAO,QAAQ,EAAE;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AACA,IAAM,cAAc,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,aAAa;AAAA,EACzC,GAAG;AAAA,EACH;AAAA,EACA,QAAQ;AAAA,EACR;AACF,IAAI;AAAA,EACF;AAAA,EACA,GAAG;AAAA,EACH;AAAA,EACA,OAAO;AACT,CAAC;AACD,IAAM,aAAa,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,OAAO;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAaA,SAAS,QAAQ,OAAO;AACtB,QAAM,gBAAgB,iBAAiB;AACvC,QAAM;AAAA,IACF;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,aAAa,cAAc,eAAe;AAAA,IAC9C,MAAM,SAAO,GAAG,IAAI,QAAQ,IAAI,IAAI,SAAS;AAAA,IAC7C,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,CAAC;AACD,aAAoB,oBAAAE,KAAW,iBAAU;AAAA,IACvC,UAAU,WAAW,CAAC,OAAO;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,OAAAD;AAAA,MACA;AAAA,IACF,UAAmB,oBAAAC,KAAK,YAAY,SAAS;AAAA,MAC3C,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,MACA,OAAOD;AAAA,IACT,GAAG,OAAO;AAAA,MACR;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AACA,OAAwC,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS1D,eAAe,mBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AD9MJ,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAY9B,IAAM,WAA8B,kBAAW,SAASC,UAAS,OAAO,KAAK;AAC3E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,MAAM;AACjB,QAAM,aAAa,GAAG,EAAE;AACxB,QAAM,sBAAsB,WAAW,gBAAgB,WAAW,UAAa,OAAO,KAAK,UAAQ,KAAK,WAAW,YAAY;AAC/H,QAAM,oBAAoB;AAAA,IACxB,WAAW;AAAA,IACX,MAAM,MAAM,KAAK;AAAA,MACf,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI,OAAK;AAClC,YAAI,MAAM;AACV,iBAAS,QAAQ,UAAU,EAAE,SAAS,OAAO,UAAU,YAAY,OAAO,OAAO,CAAC,GAAG;AAAA,MACvF,CAAC,CAAC;AAAA,IACJ,GAAG,CAAC,GAAG,UAAU,KAAK;AAAA,EACxB;AACA,QAAM,2BAA2B,SAAS,CAAC,GAAG,sBAAsB;AAAA,IAClE,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,EACL,GAAG,aAAa;AAChB,aAAoB,oBAAAC,MAAM,0BAA0B;AAAA,IAClD;AAAA,IACA,QAAQ,OAAO,IAAI,OAAK,SAAS;AAAA,MAC/B,MAAM;AAAA,IACR,GAAG,GAAG;AAAA,MACJ,QAAQ,sBAAsB,eAAe;AAAA,IAC/C,CAAC,CAAC;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,SAAS,OAAO,QAAQ,sBAAsB,SAAY,CAAC,SAAS;AAAA,MACzE,IAAI;AAAA,IACN,GAAG,iBAAiB,CAAC;AAAA,IACrB,OAAO,SAAS,OAAO,QAAQ,sBAAsB,CAAC,SAAS;AAAA,MAC7D,IAAI;AAAA,IACN,GAAG,iBAAiB,CAAC,IAAI;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA,sBAAsB,WAAW,OAAO,SAAS,QAAQ,aAAa,WAAW,iBAAiB,OAAO,SAAS,cAAc,OAAO,WAAW,iBAAiB,OAAO,SAAS,cAAc,OAAO;AAAA,IACxM,UAAU,KAAc,oBAAAC,KAAK,KAAK;AAAA,MAChC,UAAU,QAAQ,UAAU;AAAA,MAC5B,cAAuB,oBAAAA,KAAK,SAAS;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC,OAAgB,oBAAAA,KAAK,YAAY;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,OAAgB,oBAAAA,KAAK,cAAc,SAAS,CAAC,GAAG,QAAQ;AAAA,MACvD;AAAA,MACA;AAAA,IACF,CAAC,CAAC,OAAgB,oBAAAA,KAAK,qBAAqB,SAAS,CAAC,GAAG,wBAAwB,CAAC,OAAgB,oBAAAA,KAAK,eAAe,SAAS,CAAC,GAAG,SAAS;AAAA,MAC1I;AAAA,MACA;AAAA,IACF,CAAC,CAAC,OAAgB,oBAAAA,KAAK,gBAAgB;AAAA,MACrC,IAAI;AAAA,IACN,CAAC,GAAG,QAAQ;AAAA,EACd,CAAC;AACH,CAAC;AACD,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY3D,eAAe,mBAAAC,QAAU,MAAM;AAAA,IAC7B,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC3C,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC7C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC/C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,SAAS,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,QAAQ,mBAAAA,QAAU;AAAA,EAClB,QAAQ,mBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlD,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC7C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC5C,QAAQ,mBAAAA,QAAU;AAAA,IAClB,UAAU,mBAAAA,QAAU,MAAM;AAAA,MACxB,YAAY,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,CAAC,EAAE;AAAA,MACzD,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,UAAU,KAAK,CAAC,EAAE;AAAA,IACzD,CAAC;AAAA,IACD,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,KAAK,mBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC9C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACxC,OAAO,mBAAAA,QAAU;AAAA,IACjB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACxC,SAAS,mBAAAA,QAAU;AAAA,IACnB,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,MAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACzD,CAAC;AAAA,IACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA,IAClD,OAAO,mBAAAA,QAAU;AAAA,IACjB,aAAa,mBAAAA,QAAU,MAAM,CAAC,aAAa,UAAU,QAAQ,cAAc,QAAQ,CAAC;AAAA,IACpF,YAAY,mBAAAA,QAAU,MAAM,CAAC,cAAc,aAAa,cAAc,aAAa,QAAQ,SAAS,CAAC;AAAA,IACrG,MAAM,mBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAC7B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,UAAU,mBAAAA,QAAU;AAAA,IACpB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,SAAS,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC5C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,OAAO,mBAAAA,QAAU;AAAA,IACjB,GAAG,mBAAAA,QAAU;AAAA,IACb,GAAG,mBAAAA,QAAU;AAAA,EACf,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AACJ,IAAI;;;AG1ZJ;AACA,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACFtB;AACA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACFtB;AACA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AAItB,IAAAC,uBAA4B;AAW5B,SAAS,QAAQ,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAAC;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,kBAAW,kBAAkB;AACvC,QAAM,0BAA0B,wBAAwB,OAAO,cAAc;AAC7E,QAAM,YAAkB,eAAQ,MAAM;AACpC,UAAM,eAAe,yBAAyB,MAAM;AACpD,UAAM,eAAe,yBAAyB,MAAM;AACpD,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM;AACpC,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM;AACpC,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM;AACpC,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM;AACpC,UAAM,OAAO,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK,QAAQ,KAAK,GAAG;AAC9C,YAAM,eAAe,OAAO,KAAK,CAAC;AAClC,YAAM,IAAI,aAAa,aAAa,CAAC;AACrC,YAAM,IAAI,aAAa,aAAa,CAAC;AACrC,YAAM,YAAY,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK;AAC7E,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU,OAAO;AAAA,QACjB,WAAW;AAAA,MACb;AACA,UAAI,WAAW;AACb,aAAK,KAAK;AAAA,UACR;AAAA,UACA;AAAA,UACA,SAAS,CAAC,iBAAiB,MAAM,UAAU,OAAO,cAAc,KAAK,WAAW,MAAM,UAAU,OAAO,cAAc;AAAA,UACrH,kBAAkB,wBAAwB,QAAQ;AAAA,UAClD,IAAI,aAAa;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,QAAQ,yBAAyB,MAAM,OAAO,MAAM,OAAO,gBAAgB,OAAO,EAAE,CAAC;AACjG,aAAoB,qBAAAC,KAAK,KAAK;AAAA,IAC5B,UAAU,UAAU,IAAI,mBAA0B,qBAAAA,KAAK,UAAU,SAAS;AAAA,MACxE,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,WAAW,aAAa,UAAU,CAAC,KAAK,UAAU,CAAC;AAAA,MACnD,MAAMD;AAAA,MACN,SAAS,UAAU,WAAW,OAAO;AAAA,IACvC,GAAG,UAAU,gBAAgB,GAAG,UAAU,EAAE,CAAC;AAAA,EAC/C,CAAC;AACH;AACA,OAAwC,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,OAAO,mBAAAE,QAAU,OAAO;AAAA,EACxB,YAAY,mBAAAA,QAAU,OAAO;AAAA,EAC7B,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,MACtC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,MAC9D,GAAG,mBAAAA,QAAU,OAAO;AAAA,MACpB,GAAG,mBAAAA,QAAU,OAAO;AAAA,IACtB,CAAC,CAAC,EAAE;AAAA,IACJ,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,MAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACzD,CAAC;AAAA,IACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAC9D,OAAO,mBAAAA,QAAU;AAAA,IACjB,YAAY,mBAAAA,QAAU;AAAA,IACtB,MAAM,mBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,IACnC,gBAAgB,mBAAAA,QAAU,KAAK;AAAA,IAC/B,UAAU,mBAAAA,QAAU;AAAA,IACpB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,EAAE;AAAA,EACH,QAAQ,mBAAAA,QAAU,KAAK;AAAA,EACvB,QAAQ,mBAAAA,QAAU,KAAK;AACzB,IAAI;;;AD9FJ,IAAAC,uBAA4B;AAW5B,SAAS,YAAY,OAAO;AAC1B,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAmB,kBAAW,aAAa,EAAE;AACnD,QAAM,WAAiB,kBAAW,gBAAgB;AAClD,MAAI,eAAe,QAAW;AAC5B,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,iBAAiB,SAAS,CAAC;AACjC,QAAM,iBAAiB,SAAS,CAAC;AACjC,QAAM,gBAAgB,iBAAiB,SAAS,OAAO,SAAS,MAAM,YAAY,OAAO,iBAAiB;AAC1G,aAAoB,qBAAAC,KAAW,iBAAU;AAAA,IACvC,UAAU,YAAY,IAAI,cAAY;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAAC;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,YAAM,SAAS,MAAM,YAAY,OAAO,WAAW,cAAc,EAAE;AACnE,YAAM,SAAS,MAAM,YAAY,OAAO,WAAW,cAAc,EAAE;AACnE,iBAAoB,qBAAAD,KAAK,cAAc,SAAS;AAAA,QAC9C;AAAA,QACA;AAAA,QACA,OAAOC;AAAA,QACP,YAAY,cAAc,OAAO,aAAa;AAAA,QAC9C,QAAQ,OAAO,QAAQ;AAAA,MACzB,GAAG,aAAa,OAAO,SAAS,UAAU,OAAO,GAAG,EAAE;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AACH;AACA,OAAwC,YAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS9D,WAAW,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ADpEJ,IAAAC,uBAA4B;AAC5B,IAAAA,uBAA8B;AAW9B,IAAM,eAAkC,mBAAW,SAASC,cAAa,OAAO,KAAK;AACnF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,aAAoB,qBAAAC,MAAM,0BAA0B;AAAA,IAClD;AAAA,IACA,QAAQ,OAAO,IAAI,OAAK,SAAS;AAAA,MAC/B,MAAM;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,KAAc,qBAAAC,KAAK,YAAY;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,OAAgB,qBAAAA,KAAK,aAAa;AAAA,MACjC;AAAA,MACA;AAAA,IACF,CAAC,OAAgB,qBAAAA,KAAK,cAAc,SAAS,CAAC,GAAG,QAAQ;AAAA,MACvD;AAAA,MACA;AAAA,IACF,CAAC,CAAC,OAAgB,qBAAAA,KAAK,qBAAqB,SAAS;AAAA,MACnD,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG,aAAa,CAAC,OAAgB,qBAAAA,KAAK,eAAe,SAAS;AAAA,MAC5D,SAAS;AAAA,IACX,GAAG,OAAO,CAAC,GAAG,QAAQ;AAAA,EACxB,CAAC;AACH,CAAC;AACD,OAAwC,aAAa,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,eAAe,mBAAAC,QAAU,MAAM;AAAA,IAC7B,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC3C,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC7C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC/C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,SAAS,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC7C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC5C,QAAQ,mBAAAA,QAAU;AAAA,IAClB,UAAU,mBAAAA,QAAU,MAAM;AAAA,MACxB,YAAY,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,CAAC,EAAE;AAAA,MACzD,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,UAAU,KAAK,CAAC,EAAE;AAAA,IACzD,CAAC;AAAA,IACD,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,KAAK,mBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC9C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACxC,OAAO,mBAAAA,QAAU;AAAA,IACjB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,MACtC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,MAC9D,GAAG,mBAAAA,QAAU,OAAO;AAAA,MACpB,GAAG,mBAAAA,QAAU,OAAO;AAAA,IACtB,CAAC,CAAC,EAAE;AAAA,IACJ,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,MAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACzD,CAAC;AAAA,IACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,YAAY,mBAAAA,QAAU;AAAA,IACtB,MAAM,mBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC;AAAA,IACjC,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,UAAU,mBAAAA,QAAU;AAAA,IACpB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,SAAS,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC5C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,OAAO,mBAAAA,QAAU;AAAA,IACjB,GAAG,mBAAAA,QAAU;AAAA,IACb,GAAG,mBAAAA,QAAU;AAAA,EACf,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AACJ,IAAI;;;AGzWJ;AACA,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;AAOtB,IAAAC,uBAA4B;AAC5B,IAAAA,uBAA8B;AAC9B,IAAM,2BAA2B;AAAA,EAC/B,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT;AAWA,IAAM,iBAAoC,mBAAW,SAASC,gBAAe,OAAO,KAAK;AACvF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB,WAAS,UAAU,OAAO,KAAK,MAAM,SAAS;AAAA,IAC/D;AAAA,IACA,QAAQ;AAAA,IACR,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,oBAAoB,iBAAiB,aAAa,QAAQ;AAAA,IAC9D,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,EACL;AACA,QAAM,gBAAgB,SAAS,CAAC,GAAG,mBAAmB,eAAe;AACrE,aAAoB,qBAAAC,MAAM,0BAA0B;AAAA,IAClD;AAAA,IACA,QAAQ,CAAC,SAAS;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,GAAG,aAAa,QAAQ,CAAC,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,kBAAkB,CAAC;AAAA,IACrB,CAAC,CAAC;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC,SAAS;AAAA,MACf,IAAI;AAAA,MACJ,WAAW,aAAa,QAAQ,SAAS;AAAA,MACzC,MAAM,MAAM,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,MACf,GAAG,CAAC,GAAG,UAAU,KAAK;AAAA,MACtB,aAAa,UAAU;AAAA,IACzB,GAAG,KAAK,CAAC;AAAA,IACT;AAAA,IACA;AAAA,IACA,sBAAsB,CAAC,gBAAgB,WAAW,OAAO,SAAS,QAAQ,aAAa,YAAY,iBAAiB,OAAO,SAAS,cAAc,OAAO,WAAW,iBAAiB,OAAO,SAAS,cAAc,OAAO;AAAA,IAC1N,UAAU,CAAC,aAAa,aAAsB,qBAAAC,KAAK,SAAS;AAAA,MAC1D;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,QACF,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC,GAAG,aAAa,cAAuB,qBAAAD,MAAY,kBAAU;AAAA,MAC5D,UAAU,KAAc,qBAAAC,KAAK,UAAU;AAAA,QACrC;AAAA,QACA;AAAA,MACF,CAAC,OAAgB,qBAAAA,KAAK,UAAU;AAAA,QAC9B;AAAA,QACA;AAAA,MACF,CAAC,OAAgB,qBAAAA,KAAK,mBAAmB;AAAA,QACvC;AAAA,QACA;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC,OAAgB,qBAAAA,KAAK,qBAAqB,SAAS,CAAC,GAAG,aAAa,CAAC,GAAG,mBAA4B,qBAAAA,KAAK,eAAe,SAAS,CAAC,GAAG,SAAS;AAAA,MAC7I;AAAA,MACA;AAAA,IACF,CAAC,CAAC,GAAG,QAAQ;AAAA,EACf,CAAC;AACH,CAAC;AACD,OAAwC,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUjE,MAAM,mBAAAC,QAAU;AAAA,EAChB,eAAe,mBAAAA,QAAU,MAAM;AAAA,IAC7B,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC3C,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC7C,CAAC;AAAA,EACD,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,OAAO,mBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,aAAa,aAAa,WAAW,QAAQ,aAAa,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA,EAIvH,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA,EAI1C,SAAS,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYlB,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,KAAK,mBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,mBAAAA,QAAU,MAAM,CAAC,OAAO,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzC,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,SAAS,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB,mBAAAA,QAAU;AAAA,EAC1B,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,OAAO,mBAAAA,QAAU;AAAA,IACjB,GAAG,mBAAAA,QAAU;AAAA,IACb,GAAG,mBAAAA,QAAU;AAAA,EACf,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC;AACH,IAAI;", "names": ["React", "React", "import_jsx_runtime", "_jsxs", "_jsx", "React", "import_jsx_runtime", "getTextParams", "warnedOnce", "_jsxs", "_jsx", "import_jsx_runtime", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "React", "import_prop_types", "import_jsx_runtime", "color", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "color", "_jsx", "PropTypes", "import_jsx_runtime", "<PERSON><PERSON><PERSON>", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "React", "import_prop_types", "import_jsx_runtime", "color", "_jsx", "PropTypes", "import_jsx_runtime", "_jsx", "color", "PropTypes", "import_jsx_runtime", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "import_jsx_runtime", "SparkLineChart", "_jsxs", "_jsx", "PropTypes"]}