import {
  CartesianContext,
  ChartsAxis,
  ChartsAxisHighlight,
  ChartsLegend,
  ChartsTooltip,
  DEFAULT_X_AXIS_KEY,
  DrawingContext,
  InteractionContext,
  ResponsiveChartContainer,
  SeriesContext,
  Symbol,
  area_default,
  catmullRom_default,
  color,
  getIsFaded,
  getIsHighlighted,
  getSymbol,
  getValueToPositionMapper,
  line_default,
  linear_default,
  monotoneX,
  monotoneY,
  natural_default,
  stepAfter,
  stepBefore,
  step_default,
  symbolsFill,
  useInteractionItemProps,
  useSlotProps
} from "./chunk-TWO4WIIF.js";
import {
  _objectWithoutPropertiesLoose,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  init_composeClasses,
  init_generateUtilityClass,
  init_generateUtilityClasses,
  init_objectWithoutPropertiesLoose,
  init_useId,
  require_prop_types,
  styled_default,
  useId
} from "./chunk-CNC4IL3C.js";
import {
  _extends,
  init_extends
} from "./chunk-TYEPBG5V.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/x-charts/esm/LineChart/LineChart.js
init_extends();
var React10 = __toESM(require_react());
init_useId();
var import_prop_types10 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/LineChart/AreaPlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React2 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/LineChart/AreaElement.js
init_extends();
init_objectWithoutPropertiesLoose();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["id", "classes", "color", "highlightScope", "slots", "slotProps"];
function getAreaElementUtilityClass(slot) {
  return generateUtilityClass("MuiAreaElement", slot);
}
var areaElementClasses = generateUtilityClasses("MuiAreaElement", ["root", "highlighted", "faded"]);
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getAreaElementUtilityClass, classes);
};
var AreaElementPath = styled_default("path", {
  name: "MuiAreaElement",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  ownerState
}) => ({
  stroke: "none",
  fill: ownerState.isHighlighted ? color(ownerState.color).brighter(1).formatHex() : color(ownerState.color).brighter(0.5).formatHex(),
  transition: "opacity 0.2s ease-in, fill 0.2s ease-in",
  opacity: ownerState.isFaded ? 0.3 : 1
}));
AreaElementPath.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  as: import_prop_types.default.elementType,
  ownerState: import_prop_types.default.shape({
    classes: import_prop_types.default.object,
    color: import_prop_types.default.string.isRequired,
    id: import_prop_types.default.string.isRequired,
    isFaded: import_prop_types.default.bool.isRequired,
    isHighlighted: import_prop_types.default.bool.isRequired
  }).isRequired,
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
};
function AreaElement(props) {
  var _slots$area;
  const {
    id,
    classes: innerClasses,
    color: color2,
    highlightScope,
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const getInteractionItemProps = useInteractionItemProps(highlightScope);
  const {
    item
  } = React.useContext(InteractionContext);
  const isHighlighted = getIsHighlighted(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const isFaded = !isHighlighted && getIsFaded(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const ownerState = {
    id,
    classes: innerClasses,
    color: color2,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses(ownerState);
  const Area = (_slots$area = slots == null ? void 0 : slots.area) != null ? _slots$area : AreaElementPath;
  const areaProps = useSlotProps({
    elementType: Area,
    externalSlotProps: slotProps == null ? void 0 : slotProps.area,
    additionalProps: _extends({}, other, getInteractionItemProps({
      type: "line",
      seriesId: id
    }), {
      className: classes.root
    }),
    ownerState
  });
  return (0, import_jsx_runtime.jsx)(Area, _extends({}, areaProps));
}
true ? AreaElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types.default.object,
  highlightScope: import_prop_types.default.shape({
    faded: import_prop_types.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]).isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/internals/getCurve.js
function getCurveFactory(curveType) {
  switch (curveType) {
    case "catmullRom": {
      return catmullRom_default.alpha(0.5);
    }
    case "linear": {
      return linear_default;
    }
    case "monotoneX": {
      return monotoneX;
    }
    case "monotoneY": {
      return monotoneY;
    }
    case "natural": {
      return natural_default;
    }
    case "step": {
      return step_default;
    }
    case "stepBefore": {
      return stepBefore;
    }
    case "stepAfter": {
      return stepAfter;
    }
    default:
      return monotoneX;
  }
}

// node_modules/@mui/x-charts/esm/LineChart/AreaPlot.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded2 = ["slots", "slotProps"];
function AreaPlot(props) {
  const {
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const seriesData = React2.useContext(SeriesContext).line;
  const axisData = React2.useContext(CartesianContext);
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const {
    xAxis,
    yAxis,
    xAxisIds,
    yAxisIds
  } = axisData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  return (0, import_jsx_runtime2.jsx)("g", _extends({}, other, {
    children: stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.flatMap((seriesId) => {
        var _xData$map;
        const {
          xAxisKey = defaultXAxisId,
          yAxisKey = defaultYAxisId,
          stackedData,
          data,
          connectNulls
        } = series[seriesId];
        const xScale = getValueToPositionMapper(xAxis[xAxisKey].scale);
        const yScale = yAxis[yAxisKey].scale;
        const xData = xAxis[xAxisKey].data;
        if (true) {
          if (xData === void 0) {
            throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisKey}"`} should have data property to be able to display a line plot.`);
          }
          if (xData.length < stackedData.length) {
            throw new Error(`MUI-X-Charts: The data length of the x axis (${xData.length} items) is lower than the length of series (${stackedData.length} items)`);
          }
        }
        const areaPath = area_default().x((d) => xScale(d.x)).defined((_, i) => connectNulls || data[i] != null).y0((d) => d.y && yScale(d.y[0])).y1((d) => d.y && yScale(d.y[1]));
        const curve = getCurveFactory(series[seriesId].curve);
        const formattedData = (_xData$map = xData == null ? void 0 : xData.map((x, index) => ({
          x,
          y: stackedData[index]
        }))) != null ? _xData$map : [];
        const d3Data = connectNulls ? formattedData.filter((_, i) => data[i] != null) : formattedData;
        return !!series[seriesId].area && (0, import_jsx_runtime2.jsx)(AreaElement, {
          id: seriesId,
          d: areaPath.curve(curve)(d3Data) || void 0,
          color: series[seriesId].color,
          highlightScope: series[seriesId].highlightScope,
          slots,
          slotProps
        }, seriesId);
      });
    })
  }));
}
true ? AreaPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types2.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types2.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LinePlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React4 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/LineChart/LineElement.js
init_extends();
init_objectWithoutPropertiesLoose();
var React3 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded3 = ["id", "classes", "color", "highlightScope", "slots", "slotProps"];
function getLineElementUtilityClass(slot) {
  return generateUtilityClass("MuiLineElement", slot);
}
var lineElementClasses = generateUtilityClasses("MuiLineElement", ["root", "highlighted", "faded"]);
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getLineElementUtilityClass, classes);
};
var LineElementPath = styled_default("path", {
  name: "MuiLineElement",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  ownerState
}) => ({
  strokeWidth: 2,
  strokeLinejoin: "round",
  fill: "none",
  stroke: ownerState.isHighlighted ? color(ownerState.color).brighter(0.5).formatHex() : ownerState.color,
  transition: "opacity 0.2s ease-in, stroke 0.2s ease-in",
  opacity: ownerState.isFaded ? 0.3 : 1
}));
LineElementPath.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  as: import_prop_types3.default.elementType,
  ownerState: import_prop_types3.default.shape({
    classes: import_prop_types3.default.object,
    color: import_prop_types3.default.string.isRequired,
    id: import_prop_types3.default.string.isRequired,
    isFaded: import_prop_types3.default.bool.isRequired,
    isHighlighted: import_prop_types3.default.bool.isRequired
  }).isRequired,
  sx: import_prop_types3.default.oneOfType([import_prop_types3.default.arrayOf(import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.object, import_prop_types3.default.bool])), import_prop_types3.default.func, import_prop_types3.default.object])
};
function LineElement(props) {
  var _slots$line;
  const {
    id,
    classes: innerClasses,
    color: color2,
    highlightScope,
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const getInteractionItemProps = useInteractionItemProps(highlightScope);
  const {
    item
  } = React3.useContext(InteractionContext);
  const isHighlighted = getIsHighlighted(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const isFaded = !isHighlighted && getIsFaded(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const ownerState = {
    id,
    classes: innerClasses,
    color: color2,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses2(ownerState);
  const Line = (_slots$line = slots == null ? void 0 : slots.line) != null ? _slots$line : LineElementPath;
  const lineProps = useSlotProps({
    elementType: Line,
    externalSlotProps: slotProps == null ? void 0 : slotProps.line,
    additionalProps: _extends({}, other, getInteractionItemProps({
      type: "line",
      seriesId: id
    }), {
      className: classes.root
    }),
    ownerState
  });
  return (0, import_jsx_runtime3.jsx)(Line, _extends({}, lineProps));
}
true ? LineElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types3.default.object,
  highlightScope: import_prop_types3.default.shape({
    faded: import_prop_types3.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types3.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]).isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types3.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types3.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LinePlot.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var _excluded4 = ["slots", "slotProps"];
function LinePlot(props) {
  const {
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const seriesData = React4.useContext(SeriesContext).line;
  const axisData = React4.useContext(CartesianContext);
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const {
    xAxis,
    yAxis,
    xAxisIds,
    yAxisIds
  } = axisData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  return (0, import_jsx_runtime4.jsx)("g", _extends({}, other, {
    children: stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.flatMap((seriesId) => {
        var _xData$map;
        const {
          xAxisKey = defaultXAxisId,
          yAxisKey = defaultYAxisId,
          stackedData,
          data,
          connectNulls
        } = series[seriesId];
        const xScale = getValueToPositionMapper(xAxis[xAxisKey].scale);
        const yScale = yAxis[yAxisKey].scale;
        const xData = xAxis[xAxisKey].data;
        if (true) {
          if (xData === void 0) {
            throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisKey}"`} should have data property to be able to display a line plot`);
          }
          if (xData.length < stackedData.length) {
            throw new Error(`MUI-X-Charts: The data length of the x axis (${xData.length} items) is lower than the length of series (${stackedData.length} items)`);
          }
        }
        const linePath = line_default().x((d) => xScale(d.x)).defined((_, i) => connectNulls || data[i] != null).y((d) => yScale(d.y[1]));
        const curve = getCurveFactory(series[seriesId].curve);
        const formattedData = (_xData$map = xData == null ? void 0 : xData.map((x, index) => ({
          x,
          y: stackedData[index]
        }))) != null ? _xData$map : [];
        const d3Data = connectNulls ? formattedData.filter((_, i) => data[i] != null) : formattedData;
        return (0, import_jsx_runtime4.jsx)(LineElement, {
          id: seriesId,
          d: linePath.curve(curve)(d3Data) || void 0,
          color: series[seriesId].color,
          highlightScope: series[seriesId].highlightScope,
          slots,
          slotProps
        }, seriesId);
      });
    })
  }));
}
true ? LinePlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types4.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types4.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/MarkPlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React6 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/LineChart/MarkElement.js
init_extends();
init_objectWithoutPropertiesLoose();
var React5 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var _excluded5 = ["x", "y", "id", "classes", "color", "shape", "dataIndex", "highlightScope"];
function getMarkElementUtilityClass(slot) {
  return generateUtilityClass("MuiMarkElement", slot);
}
var markElementClasses = generateUtilityClasses("MuiMarkElement", ["root", "highlighted", "faded"]);
var useUtilityClasses3 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getMarkElementUtilityClass, classes);
};
var MarkElementPath = styled_default("path", {
  name: "MuiMarkElement",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  ownerState,
  theme
}) => ({
  transform: `translate(${ownerState.x}px, ${ownerState.y}px)`,
  transformOrigin: `${ownerState.x}px ${ownerState.y}px`,
  fill: (theme.vars || theme).palette.background.paper,
  stroke: ownerState.color,
  strokeWidth: 2
}));
MarkElementPath.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  as: import_prop_types5.default.elementType,
  ownerState: import_prop_types5.default.shape({
    classes: import_prop_types5.default.object,
    color: import_prop_types5.default.string.isRequired,
    id: import_prop_types5.default.string.isRequired,
    isFaded: import_prop_types5.default.bool.isRequired,
    isHighlighted: import_prop_types5.default.bool.isRequired,
    x: import_prop_types5.default.number.isRequired,
    y: import_prop_types5.default.number.isRequired
  }).isRequired,
  sx: import_prop_types5.default.oneOfType([import_prop_types5.default.arrayOf(import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object, import_prop_types5.default.bool])), import_prop_types5.default.func, import_prop_types5.default.object])
};
function MarkElement(props) {
  var _axis$x;
  const {
    x,
    y,
    id,
    classes: innerClasses,
    color: color2,
    shape,
    dataIndex,
    highlightScope
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const getInteractionItemProps = useInteractionItemProps(highlightScope);
  const {
    item,
    axis
  } = React5.useContext(InteractionContext);
  const isHighlighted = ((_axis$x = axis.x) == null ? void 0 : _axis$x.index) === dataIndex || getIsHighlighted(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const isFaded = !isHighlighted && getIsFaded(item, {
    type: "line",
    seriesId: id
  }, highlightScope);
  const ownerState = {
    id,
    classes: innerClasses,
    isHighlighted,
    isFaded,
    color: color2,
    x,
    y
  };
  const classes = useUtilityClasses3(ownerState);
  return (0, import_jsx_runtime5.jsx)(MarkElementPath, _extends({}, other, {
    ownerState,
    className: classes.root,
    d: Symbol(symbolsFill[getSymbol(shape)])()
  }, getInteractionItemProps({
    type: "line",
    seriesId: id,
    dataIndex
  })));
}
true ? MarkElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types5.default.object,
  /**
   * The index to the element in the series' data array.
   */
  dataIndex: import_prop_types5.default.number.isRequired,
  highlightScope: import_prop_types5.default.shape({
    faded: import_prop_types5.default.oneOf(["global", "none", "series"]),
    highlighted: import_prop_types5.default.oneOf(["item", "none", "series"])
  }),
  id: import_prop_types5.default.oneOfType([import_prop_types5.default.number, import_prop_types5.default.string]).isRequired,
  /**
   * The shape of the marker.
   */
  shape: import_prop_types5.default.oneOf(["circle", "cross", "diamond", "square", "star", "triangle", "wye"]).isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/MarkPlot.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var _excluded6 = ["slots", "slotProps"];
function MarkPlot(props) {
  var _slots$mark;
  const {
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded6);
  const seriesData = React6.useContext(SeriesContext).line;
  const axisData = React6.useContext(CartesianContext);
  const Mark = (_slots$mark = slots == null ? void 0 : slots.mark) != null ? _slots$mark : MarkElement;
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const {
    xAxis,
    yAxis,
    xAxisIds,
    yAxisIds
  } = axisData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  return (0, import_jsx_runtime6.jsx)("g", _extends({}, other, {
    children: stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.flatMap((seriesId) => {
        const {
          xAxisKey = defaultXAxisId,
          yAxisKey = defaultYAxisId,
          stackedData,
          data,
          showMark = true
        } = series[seriesId];
        if (showMark === false) {
          return null;
        }
        const xScale = getValueToPositionMapper(xAxis[xAxisKey].scale);
        const yScale = yAxis[yAxisKey].scale;
        const xData = xAxis[xAxisKey].data;
        const xRange = xAxis[xAxisKey].scale.range();
        const yRange = yScale.range();
        const isInRange = ({
          x,
          y
        }) => {
          if (x < Math.min(...xRange) || x > Math.max(...xRange)) {
            return false;
          }
          if (y < Math.min(...yRange) || y > Math.max(...yRange)) {
            return false;
          }
          return true;
        };
        if (xData === void 0) {
          throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisKey}"`} should have data property to be able to display a line plot`);
        }
        return xData == null ? void 0 : xData.map((x, index) => {
          const value = data[index] == null ? null : stackedData[index][1];
          return {
            x: xScale(x),
            y: value === null ? null : yScale(value),
            position: x,
            value,
            index
          };
        }).filter(({
          x,
          y,
          index,
          position,
          value
        }) => {
          if (value === null || y === null) {
            return false;
          }
          if (!isInRange({
            x,
            y
          })) {
            return false;
          }
          if (showMark === true) {
            return true;
          }
          return showMark({
            x,
            y,
            index,
            position,
            value
          });
        }).map(({
          x,
          y,
          index
        }) => {
          return (0, import_jsx_runtime6.jsx)(Mark, _extends({
            id: seriesId,
            dataIndex: index,
            shape: "circle",
            color: series[seriesId].color,
            x,
            y,
            highlightScope: series[seriesId].highlightScope
          }, slotProps == null ? void 0 : slotProps.mark), `${seriesId}-${index}`);
        });
      });
    })
  }));
}
true ? MarkPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types6.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsClipPath/ChartsClipPath.js
init_extends();
var React7 = __toESM(require_react());
var import_prop_types7 = __toESM(require_prop_types());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
function ChartsClipPath(props) {
  const {
    id,
    offset: offsetProps
  } = props;
  const {
    left,
    top,
    width,
    height
  } = React7.useContext(DrawingContext);
  const offset = _extends({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }, offsetProps);
  return (0, import_jsx_runtime7.jsx)("clipPath", {
    id,
    children: (0, import_jsx_runtime7.jsx)("rect", {
      x: left - offset.left,
      y: top - offset.top,
      width: width + offset.left + offset.right,
      height: height + offset.top + offset.bottom
    })
  });
}
true ? ChartsClipPath.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  id: import_prop_types7.default.string.isRequired,
  offset: import_prop_types7.default.shape({
    bottom: import_prop_types7.default.number,
    left: import_prop_types7.default.number,
    right: import_prop_types7.default.number,
    top: import_prop_types7.default.number
  })
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightPlot.js
init_extends();
init_objectWithoutPropertiesLoose();
var React9 = __toESM(require_react());
var import_prop_types9 = __toESM(require_prop_types());

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightElement.js
init_extends();
init_objectWithoutPropertiesLoose();
var React8 = __toESM(require_react());
var import_prop_types8 = __toESM(require_prop_types());
init_composeClasses();
init_generateUtilityClass();
init_generateUtilityClasses();
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var _excluded7 = ["x", "y", "id", "classes", "color"];
function getHighlightElementUtilityClass(slot) {
  return generateUtilityClass("MuiHighlightElement", slot);
}
var lineHighlightElementClasses = generateUtilityClasses("MuiHighlightElement", ["root"]);
var useUtilityClasses4 = (ownerState) => {
  const {
    classes,
    id
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`]
  };
  return composeClasses(slots, getHighlightElementUtilityClass, classes);
};
var HighlightElement = styled_default("circle", {
  name: "MuiHighlightElement",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  ownerState
}) => ({
  transform: `translate(${ownerState.x}px, ${ownerState.y}px)`,
  transformOrigin: `${ownerState.x}px ${ownerState.y}px`,
  fill: ownerState.color
}));
function LineHighlightElement(props) {
  const {
    x,
    y,
    id,
    classes: innerClasses,
    color: color2
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const ownerState = {
    id,
    classes: innerClasses,
    color: color2,
    x,
    y
  };
  const classes = useUtilityClasses4(ownerState);
  return (0, import_jsx_runtime8.jsx)(HighlightElement, _extends({}, other, {
    ownerState,
    className: classes.root,
    cx: 0,
    cy: 0,
    r: other.r === void 0 ? 5 : other.r
  }));
}
true ? LineHighlightElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types8.default.object,
  id: import_prop_types8.default.oneOfType([import_prop_types8.default.number, import_prop_types8.default.string]).isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightPlot.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var _excluded8 = ["slots", "slotProps"];
function LineHighlightPlot(props) {
  var _axis$x, _slots$lineHighlight;
  const {
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded8);
  const seriesData = React9.useContext(SeriesContext).line;
  const axisData = React9.useContext(CartesianContext);
  const {
    axis
  } = React9.useContext(InteractionContext);
  const highlightedIndex = (_axis$x = axis.x) == null ? void 0 : _axis$x.index;
  if (highlightedIndex === void 0) {
    return null;
  }
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const {
    xAxis,
    yAxis,
    xAxisIds,
    yAxisIds
  } = axisData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  const Element = (_slots$lineHighlight = slots == null ? void 0 : slots.lineHighlight) != null ? _slots$lineHighlight : LineHighlightElement;
  return (0, import_jsx_runtime9.jsx)("g", _extends({}, other, {
    children: stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.flatMap((seriesId) => {
        const {
          xAxisKey = defaultXAxisId,
          yAxisKey = defaultYAxisId,
          stackedData,
          data,
          disableHighlight
        } = series[seriesId];
        if (disableHighlight || data[highlightedIndex] == null) {
          return null;
        }
        const xScale = getValueToPositionMapper(xAxis[xAxisKey].scale);
        const yScale = yAxis[yAxisKey].scale;
        const xData = xAxis[xAxisKey].data;
        if (xData === void 0) {
          throw new Error(`MUI-X-Charts: ${xAxisKey === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisKey}"`} should have data property to be able to display a line plot.`);
        }
        const x = xScale(xData[highlightedIndex]);
        const y = yScale(stackedData[highlightedIndex][1]);
        return (0, import_jsx_runtime9.jsx)(Element, _extends({
          id: seriesId,
          color: series[seriesId].color,
          x,
          y
        }, slotProps == null ? void 0 : slotProps.lineHighlight), `${seriesId}`);
      });
    })
  }));
}
true ? LineHighlightPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types9.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types9.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineChart.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var LineChart = React10.forwardRef(function LineChart2(props, ref) {
  const {
    xAxis,
    yAxis,
    series,
    width,
    height,
    margin,
    colors,
    dataset,
    sx,
    tooltip,
    axisHighlight = {
      x: "line"
    },
    disableLineItemHighlight,
    legend,
    topAxis,
    leftAxis,
    rightAxis,
    bottomAxis,
    children,
    slots,
    slotProps
  } = props;
  const id = useId();
  const clipPathId = `${id}-clip-path`;
  return (0, import_jsx_runtime11.jsxs)(ResponsiveChartContainer, {
    ref,
    series: series.map((s) => _extends({
      disableHighlight: !!disableLineItemHighlight,
      type: "line"
    }, s)),
    width,
    height,
    margin,
    xAxis: xAxis != null ? xAxis : [{
      id: DEFAULT_X_AXIS_KEY,
      scaleType: "point",
      data: Array.from({
        length: Math.max(...series.map((s) => {
          var _ref, _s$data;
          return ((_ref = (_s$data = s.data) != null ? _s$data : dataset) != null ? _ref : []).length;
        }))
      }, (_, index) => index)
    }],
    yAxis,
    colors,
    dataset,
    sx,
    disableAxisListener: (tooltip == null ? void 0 : tooltip.trigger) !== "axis" && (axisHighlight == null ? void 0 : axisHighlight.x) === "none" && (axisHighlight == null ? void 0 : axisHighlight.y) === "none",
    children: [(0, import_jsx_runtime11.jsxs)("g", {
      clipPath: `url(#${clipPathId})`,
      children: [(0, import_jsx_runtime10.jsx)(AreaPlot, {
        slots,
        slotProps
      }), (0, import_jsx_runtime10.jsx)(LinePlot, {
        slots,
        slotProps
      })]
    }), (0, import_jsx_runtime10.jsx)(ChartsAxis, {
      topAxis,
      leftAxis,
      rightAxis,
      bottomAxis,
      slots,
      slotProps
    }), (0, import_jsx_runtime10.jsx)(ChartsAxisHighlight, _extends({}, axisHighlight)), (0, import_jsx_runtime10.jsx)(MarkPlot, {
      slots,
      slotProps
    }), (0, import_jsx_runtime10.jsx)(LineHighlightPlot, {
      slots,
      slotProps
    }), (0, import_jsx_runtime10.jsx)(ChartsLegend, _extends({}, legend, {
      slots,
      slotProps
    })), (0, import_jsx_runtime10.jsx)(ChartsTooltip, _extends({}, tooltip, {
      slots,
      slotProps
    })), (0, import_jsx_runtime10.jsx)(ChartsClipPath, {
      id: clipPathId
    }), children]
  });
});
true ? LineChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Object `{ x, y }` that defines how the charts highlight the mouse position along the x- and y-axes.
   * The two properties accept the following values:
   * - 'none': display nothing.
   * - 'line': display a line at the current mouse position.
   * - 'band': display a band at the current mouse position. Only available with band scale.
   * @default { x: 'line' }
   */
  axisHighlight: import_prop_types10.default.shape({
    x: import_prop_types10.default.oneOf(["band", "line", "none"]),
    y: import_prop_types10.default.oneOf(["band", "line", "none"])
  }),
  /**
   * Indicate which axis to display the bottom of the charts.
   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.
   * @default xAxisIds[0] The id of the first provided axis
   */
  bottomAxis: import_prop_types10.default.oneOfType([import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    position: import_prop_types10.default.oneOf(["bottom", "top"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number
  }), import_prop_types10.default.string]),
  children: import_prop_types10.default.node,
  className: import_prop_types10.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default blueberryTwilightPalette
   */
  colors: import_prop_types10.default.oneOfType([import_prop_types10.default.arrayOf(import_prop_types10.default.string), import_prop_types10.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types10.default.arrayOf(import_prop_types10.default.object),
  desc: import_prop_types10.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types10.default.bool,
  /**
   * If `true`, render the line highlight item.
   */
  disableLineItemHighlight: import_prop_types10.default.bool,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   * @default undefined
   */
  height: import_prop_types10.default.number,
  /**
   * Indicate which axis to display the left of the charts.
   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.
   * @default yAxisIds[0] The id of the first provided axis
   */
  leftAxis: import_prop_types10.default.oneOfType([import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    position: import_prop_types10.default.oneOf(["left", "right"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number
  }), import_prop_types10.default.string]),
  /**
   * @deprecated Consider using `slotProps.legend` instead.
   */
  legend: import_prop_types10.default.shape({
    classes: import_prop_types10.default.object,
    direction: import_prop_types10.default.oneOf(["column", "row"]),
    hidden: import_prop_types10.default.bool,
    position: import_prop_types10.default.shape({
      horizontal: import_prop_types10.default.oneOf(["left", "middle", "right"]).isRequired,
      vertical: import_prop_types10.default.oneOf(["bottom", "middle", "top"]).isRequired
    }),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object
  }),
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   * @default object Depends on the charts type.
   */
  margin: import_prop_types10.default.shape({
    bottom: import_prop_types10.default.number,
    left: import_prop_types10.default.number,
    right: import_prop_types10.default.number,
    top: import_prop_types10.default.number
  }),
  /**
   * Indicate which axis to display the right of the charts.
   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.
   * @default null
   */
  rightAxis: import_prop_types10.default.oneOfType([import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    position: import_prop_types10.default.oneOf(["left", "right"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number
  }), import_prop_types10.default.string]),
  series: import_prop_types10.default.arrayOf(import_prop_types10.default.shape({
    area: import_prop_types10.default.bool,
    color: import_prop_types10.default.string,
    connectNulls: import_prop_types10.default.bool,
    curve: import_prop_types10.default.oneOf(["catmullRom", "linear", "monotoneX", "monotoneY", "natural", "step", "stepAfter", "stepBefore"]),
    data: import_prop_types10.default.arrayOf(import_prop_types10.default.number),
    dataKey: import_prop_types10.default.string,
    disableHighlight: import_prop_types10.default.bool,
    highlightScope: import_prop_types10.default.shape({
      faded: import_prop_types10.default.oneOf(["global", "none", "series"]),
      highlighted: import_prop_types10.default.oneOf(["item", "none", "series"])
    }),
    id: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    label: import_prop_types10.default.string,
    showMark: import_prop_types10.default.oneOfType([import_prop_types10.default.func, import_prop_types10.default.bool]),
    stack: import_prop_types10.default.string,
    stackOffset: import_prop_types10.default.oneOf(["diverging", "expand", "none", "silhouette", "wiggle"]),
    stackOrder: import_prop_types10.default.oneOf(["appearance", "ascending", "descending", "insideOut", "none", "reverse"]),
    type: import_prop_types10.default.oneOf(["line"]),
    valueFormatter: import_prop_types10.default.func,
    xAxisKey: import_prop_types10.default.string,
    yAxisKey: import_prop_types10.default.string
  })).isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types10.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types10.default.object,
  sx: import_prop_types10.default.oneOfType([import_prop_types10.default.arrayOf(import_prop_types10.default.oneOfType([import_prop_types10.default.func, import_prop_types10.default.object, import_prop_types10.default.bool])), import_prop_types10.default.func, import_prop_types10.default.object]),
  title: import_prop_types10.default.string,
  tooltip: import_prop_types10.default.shape({
    axisContent: import_prop_types10.default.elementType,
    classes: import_prop_types10.default.object,
    itemContent: import_prop_types10.default.elementType,
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    trigger: import_prop_types10.default.oneOf(["axis", "item", "none"])
  }),
  /**
   * Indicate which axis to display the top of the charts.
   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.
   * @default null
   */
  topAxis: import_prop_types10.default.oneOfType([import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    position: import_prop_types10.default.oneOf(["bottom", "top"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number
  }), import_prop_types10.default.string]),
  viewBox: import_prop_types10.default.shape({
    height: import_prop_types10.default.number,
    width: import_prop_types10.default.number,
    x: import_prop_types10.default.number,
    y: import_prop_types10.default.number
  }),
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   * @default undefined
   */
  width: import_prop_types10.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.
   */
  xAxis: import_prop_types10.default.arrayOf(import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    data: import_prop_types10.default.array,
    dataKey: import_prop_types10.default.string,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    hideTooltip: import_prop_types10.default.bool,
    id: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    max: import_prop_types10.default.oneOfType([import_prop_types10.default.instanceOf(Date), import_prop_types10.default.number]),
    min: import_prop_types10.default.oneOfType([import_prop_types10.default.instanceOf(Date), import_prop_types10.default.number]),
    position: import_prop_types10.default.oneOf(["bottom", "left", "right", "top"]),
    reverse: import_prop_types10.default.bool,
    scaleType: import_prop_types10.default.oneOf(["band", "linear", "log", "point", "pow", "sqrt", "time", "utc"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number,
    valueFormatter: import_prop_types10.default.func
  })),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.
   */
  yAxis: import_prop_types10.default.arrayOf(import_prop_types10.default.shape({
    axisId: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    classes: import_prop_types10.default.object,
    data: import_prop_types10.default.array,
    dataKey: import_prop_types10.default.string,
    disableLine: import_prop_types10.default.bool,
    disableTicks: import_prop_types10.default.bool,
    fill: import_prop_types10.default.string,
    hideTooltip: import_prop_types10.default.bool,
    id: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]),
    label: import_prop_types10.default.string,
    labelFontSize: import_prop_types10.default.number,
    labelStyle: import_prop_types10.default.object,
    max: import_prop_types10.default.oneOfType([import_prop_types10.default.instanceOf(Date), import_prop_types10.default.number]),
    min: import_prop_types10.default.oneOfType([import_prop_types10.default.instanceOf(Date), import_prop_types10.default.number]),
    position: import_prop_types10.default.oneOf(["bottom", "left", "right", "top"]),
    reverse: import_prop_types10.default.bool,
    scaleType: import_prop_types10.default.oneOf(["band", "linear", "log", "point", "pow", "sqrt", "time", "utc"]),
    slotProps: import_prop_types10.default.object,
    slots: import_prop_types10.default.object,
    stroke: import_prop_types10.default.string,
    tickFontSize: import_prop_types10.default.number,
    tickInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.array, import_prop_types10.default.func]),
    tickLabelInterval: import_prop_types10.default.oneOfType([import_prop_types10.default.oneOf(["auto"]), import_prop_types10.default.func]),
    tickLabelStyle: import_prop_types10.default.object,
    tickMaxStep: import_prop_types10.default.number,
    tickMinStep: import_prop_types10.default.number,
    tickNumber: import_prop_types10.default.number,
    tickSize: import_prop_types10.default.number,
    valueFormatter: import_prop_types10.default.func
  }))
} : void 0;

export {
  ChartsClipPath,
  getAreaElementUtilityClass,
  areaElementClasses,
  AreaElementPath,
  AreaElement,
  AreaPlot,
  getLineElementUtilityClass,
  lineElementClasses,
  LineElementPath,
  LineElement,
  LinePlot,
  getMarkElementUtilityClass,
  markElementClasses,
  MarkElement,
  MarkPlot,
  getHighlightElementUtilityClass,
  lineHighlightElementClasses,
  LineHighlightElement,
  LineHighlightPlot,
  LineChart
};
//# sourceMappingURL=chunk-HE4G7SGB.js.map
