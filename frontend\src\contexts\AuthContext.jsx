import { createContext, useState, useContext, useEffect, useCallback, useRef } from 'react';
import { jwtDecode } from 'jwt-decode';
import api from '../services/api';
import axios from 'axios';
import { useTenant } from './TenantContext';

// Create context
const AuthContext = createContext(null);

// Session timeout in milliseconds (30 minutes)
const SESSION_TIMEOUT = 30 * 60 * 1000;

export const AuthProvider = ({ children }) => {
  // State
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sessionExpiring, setSessionExpiring] = useState(false);
  const [tenantChanged, setTenantChanged] = useState(false);

  // Refs for timers and activity tracking
  const sessionTimeoutIdRef = useRef(null);
  const activityIntervalIdRef = useRef(null);
  const lastActivityRef = useRef(Date.now());

  // Utility functions (no dependencies)
  const isTokenExpired = (token) => {
    if (!token) return true;

    try {
      const decoded = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime;
    } catch (error) {
      console.error('Error decoding token:', error);
      return true;
    }
  };

  // Function to get tenant information from token
  const getTenantFromToken = (token) => {
    if (!token) return null;

    try {
      const decoded = jwtDecode(token);
      return decoded.tenant || null;
    } catch (error) {
      console.error('Error getting tenant from token:', error);
      return null;
    }
  };

  const getTokenExpirationTime = (token) => {
    if (!token) return 0;

    try {
      const decoded = jwtDecode(token);
      return (decoded.exp * 1000) - Date.now();
    } catch (error) {
      console.error('Error decoding token:', error);
      return 0;
    }
  };

  // Helper function to clear auth state (without API calls)
  const clearAuthState = useCallback(() => {
    // Clear local storage
    localStorage.removeItem('ui_data');
    sessionStorage.removeItem('lastActivity');

    // Clear state
    setUser(null);

    // Clear timeout
    if (sessionTimeoutIdRef.current) {
      clearTimeout(sessionTimeoutIdRef.current);
      sessionTimeoutIdRef.current = null;
    }

    // Clear activity tracking
    if (activityIntervalIdRef.current) {
      clearInterval(activityIntervalIdRef.current);
      activityIntervalIdRef.current = null;
    }
  }, []);

  // Function to set up session timeout
  const setupSessionTimeout = (token) => {
    // Clear any existing timeout
    if (sessionTimeoutIdRef.current) {
      clearTimeout(sessionTimeoutIdRef.current);
    }

    // Get token expiration time
    const expirationTime = getTokenExpirationTime(token);

    // Set warning timeout (5 minutes before expiration)
    const warningTime = expirationTime - (5 * 60 * 1000);

    if (warningTime > 0) {
      sessionTimeoutIdRef.current = setTimeout(() => {
        setSessionExpiring(true);
      }, warningTime);
    }
  };

  // Activity tracking function
  const updateActivity = useCallback(() => {
    const currentTime = Date.now();
    lastActivityRef.current = currentTime;
    sessionStorage.setItem('lastActivity', currentTime.toString());
  }, []);

  // Function to handle user logout
  const logout = useCallback(async (redirectToLogin = true) => {
    console.log('Logging out user...');

    // Clear all client-side state IMMEDIATELY to prevent routing issues
    const clearClientState = () => {
      console.log('Clearing client-side state...');

      // Set a flag to prevent automatic login after logout
      localStorage.setItem('prevent_auto_login', 'true');

      // Clear localStorage and sessionStorage
      localStorage.removeItem('ui_data');
      sessionStorage.removeItem('lastActivity');

      // Clear application state IMMEDIATELY
      setUser(null);
      setSessionExpiring(false);

      // Clear all timers and intervals
      if (sessionTimeoutIdRef.current) {
        clearTimeout(sessionTimeoutIdRef.current);
        sessionTimeoutIdRef.current = null;
      }

      if (activityIntervalIdRef.current) {
        clearInterval(activityIntervalIdRef.current);
        activityIntervalIdRef.current = null;
      }

      if (authCheckIntervalRef.current) {
        clearInterval(authCheckIntervalRef.current);
        authCheckIntervalRef.current = null;
      }

      // Remove event listeners
      window.removeEventListener('mousemove', updateActivity);
      window.removeEventListener('keydown', updateActivity);
      window.removeEventListener('click', updateActivity);
      window.removeEventListener('scroll', updateActivity);

      // Reset initialization flags
      isInitializingRef.current = false;
      hasInitializedRef.current = false;

      // Dispatch event to notify other components about logout
      window.dispatchEvent(new CustomEvent('user-logged-out'));
    };

    // Function to clear cookies on the client side (for non-HttpOnly cookies)
    const clearClientCookies = () => {
      console.log('Clearing client-side cookies...');

      // Clear known cookies with all possible paths and domains
      const cookieNames = ['access_token', 'refresh_token', 'ui_data'];
      const domains = [window.location.hostname, 'localhost', ''];
      const paths = ['/', '/api', '/api/v1', '/api/v1/auth', '/api/v1/auth/token', '/api/v1/auth/token/refresh/'];

      cookieNames.forEach(name => {
        domains.forEach(domain => {
          paths.forEach(path => {
            // Try with domain
            if (domain) {
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
              document.cookie = `${name}=; max-age=0; path=${path}; domain=${domain}`;
            }
            // Try without domain
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
            document.cookie = `${name}=; max-age=0; path=${path}`;
          });
        });
      });

      console.log('Client-side cookies after clearing:', document.cookie);
    };

    // 1. Clear client-side state IMMEDIATELY to prevent routing issues
    clearClientState();

    try {
      // 2. Call the backend logout endpoint to invalidate the token and clear HTTP-only cookies
      const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

      // Generate a cache-busting query parameter
      const cacheBuster = `_=${Date.now()}`;

      // Use fetch API with credentials included to ensure cookies are sent
      console.log('Calling logout endpoint...');

      // First, try to call the logout endpoint
      let response = await fetch(`${baseURL}/api/v1/auth/logout/?${cacheBuster}`, {
        method: 'POST',
        credentials: 'include', // Important: include credentials to send the cookies
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      // Verify the response
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Logout endpoint failed:', response.status, errorData);
        throw new Error(errorData.message || 'Logout failed');
      }

      // Check Set-Cookie headers
      console.log('Logout response status:', response.status);

      // Log all response headers
      const headers = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });
      console.log('Logout response headers:', headers);

      // Also call the clear-cookies endpoint as a backup
      console.log('Calling clear-cookies endpoint as backup...');
      const clearResponse = await fetch(`${baseURL}/api/v1/auth/clear-cookies/?${cacheBuster}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (response.ok) {
        console.log('Successfully logged out on server side');

        // Check response headers
        const headers = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });
        console.log('Response headers:', headers);

        // Log all cookies after logout
        console.log('Cookies after server logout:', document.cookie);
      } else {
        console.error('Server logout failed with status:', response.status);
        // Continue with client-side cleanup even if server logout fails
      }

      // 3. Clear client-side cookies
      clearClientCookies();

      // 4. As a double-check, also call the clear-cookies endpoint
      console.log('Calling clear-cookies endpoint as a backup...');
      try {
        const clearResponse = await fetch(`${baseURL}/api/v1/auth/clear-cookies/?${Date.now()}`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (clearResponse.ok) {
          console.log('Successfully called clear-cookies endpoint');

          // Check response headers
          const clearHeaders = {};
          clearResponse.headers.forEach((value, key) => {
            clearHeaders[key] = value;
          });
          console.log('Clear-cookies response headers:', clearHeaders);
        } else {
          console.error('Clear-cookies endpoint failed with status:', clearResponse.status);
        }
      } catch (clearError) {
        console.error('Error calling clear-cookies endpoint:', clearError);
      }

      // 5. Log final cookie state
      console.log('Final cookie state after logout:', document.cookie);

      // 6. Force a page reload to ensure all state is cleared
      if (redirectToLogin) {
        console.log('Forcing page reload to clear any remaining state');
        // Set a flag to indicate we're coming from logout
        localStorage.setItem('logout_redirect', 'true');

        // Use window.location.href to navigate to login page
        // This will trigger a full page reload and clear all state
        window.location.href = '/login';
      }

    } catch (error) {
      console.error('Logout error:', error);

      // Set the flag to prevent automatic login even if logout fails
      // (Note: clearClientState was already called at the beginning of logout)
      localStorage.setItem('prevent_auto_login', 'true');

      // Try to clear client-side cookies
      clearClientCookies();

      // As a fallback, try the clear-cookies endpoint
      try {
        fetch(`${baseURL}/api/v1/auth/clear-cookies/?${Date.now()}`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        }).catch(err => console.error('Error calling clear-cookies endpoint:', err));
      } catch (clearError) {
        console.error('Error setting up clear-cookies call:', clearError);
      }

      // Force a page reload to ensure all state is cleared
      if (redirectToLogin) {
        console.log('Forcing page reload to clear any remaining state (error handler)');
        // Set a flag to indicate we're coming from logout
        localStorage.setItem('logout_redirect', 'true');

        // Use window.location.href to navigate to login page
        // This will trigger a full page reload and clear all state
        window.location.href = '/login';
      }
    }

    // Note: We've already cleared state and cookies in the try/catch blocks
    // This code is now redundant since we're using the clearClientState helper function

  }, [updateActivity]);

  // Function to refresh the token
  const refreshToken = useCallback(async () => {
    try {
      // With HTTP-only cookies, we don't need to send the refresh token
      // The server will get it from the cookies
      const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
      const response = await axios.post(
        `${baseURL}/api/v1/auth/token/refresh/`,
        {},  // Empty body since token is in cookie
        {
          headers: { 'Content-Type': 'application/json' },
          withCredentials: true  // Important: send cookies with request
        }
      );

      // The server will set the new access token as a cookie
      // We'll also get it in the response for backward compatibility
      const { access } = response.data;

      // Store UI-safe data from cookies in localStorage
      try {
        const uiDataCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('ui_data='))
          ?.split('=')[1];

        if (uiDataCookie) {
          const uiData = JSON.parse(decodeURIComponent(uiDataCookie));
          localStorage.setItem('ui_data', JSON.stringify(uiData));

          // Get tenant from UI data
          const newTenant = uiData.tenant_id;

          // Dispatch tenant changed event if we have a tenant
          if (newTenant) {
            console.log('Tenant from refreshed token:', newTenant);
            window.dispatchEvent(new CustomEvent('tenant-changed', {
              detail: { tenant: newTenant }
            }));
          }
        }
      } catch (e) {
        console.error('Error parsing UI data from cookie:', e);
      }

      // Reset session timeout
      setupSessionTimeout(access);

      return access;
    } catch (error) {
      console.error('Error refreshing token:', error);
      // Clear data on refresh failure
      clearAuthState();
      return null;
    }
  }, [clearAuthState, setupSessionTimeout]);

  // Function to set up activity tracking
  const setupActivityTracking = useCallback(() => {
    // Clear any existing interval
    if (activityIntervalIdRef.current) {
      clearInterval(activityIntervalIdRef.current);
      activityIntervalIdRef.current = null;
    }

    // Set initial activity time
    updateActivity();

    // Set up interval to check for inactivity
    activityIntervalIdRef.current = setInterval(() => {
      const currentTime = Date.now();
      const lastActivityTime = parseInt(sessionStorage.getItem('lastActivity') || lastActivityRef.current);

      // If user has been inactive for SESSION_TIMEOUT, log them out
      if (currentTime - lastActivityTime > SESSION_TIMEOUT) {
        console.log('User inactive for too long, logging out');
        // Use a local variable to avoid closure issues
        const logoutFn = logout;
        if (logoutFn) {
          logoutFn(true);
        }
      }
    }, 60000); // Check every minute

    // Return a cleanup function
    return () => {
      if (activityIntervalIdRef.current) {
        clearInterval(activityIntervalIdRef.current);
        activityIntervalIdRef.current = null;
      }
    };
  }, [updateActivity]);

  // Function to extend session
  const extendSession = useCallback(async () => {
    try {
      const newToken = await refreshToken();
      if (newToken) {
        setSessionExpiring(false);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error extending session:', error);
      return false;
    }
  }, [refreshToken]);

  // Function to extract tenant from email domain
  const extractTenantFromEmail = (email) => {
    if (!email || !email.includes('@')) {
      console.error('Invalid email format: Email is required and must contain @');
      throw new Error('Invalid email format: Email is required and must contain @');
    }

    console.log('Extracting tenant from email:', email);

    // Extract the domain part
    const domain = email.split('@')[1];
    console.log('Email domain:', domain);

    // Special case for public tenant
    if (domain === 'public.localhost' || domain === 'moe.gov.et') {
      console.log('Detected public tenant from email domain');
      return 'public';
    }

    // Handle university domains with .edu.et (e.g., <EMAIL> -> uog)
    if (domain && domain.includes('.edu.et')) {
      const universityCode = domain.split('.')[0];
      if (!universityCode) {
        console.error('Invalid university email: Missing university code in domain');
        throw new Error('Invalid university email: Missing university code in domain');
      }
      console.log(`Extracted university code from email domain: ${universityCode}`);
      return universityCode;
    }

    // Handle development environment (localhost)
    if (domain === 'localhost') {
      // Extract the tenant from the local part (e.g., uog@localhost -> uog)
      const localPart = email.split('@')[0];
      console.log('Email local part:', localPart);

      // Special case for admin users
      if (localPart === 'admin') {
        console.log('Admin user detected, using public tenant');
        return 'public';
      }

      // Handle format like tenant.user@localhost (e.g., uog.user@localhost -> uog)
      if (localPart.includes('.')) {
        const tenant = localPart.split('.')[0];
        if (!tenant) {
          console.error('Invalid localhost email: Missing tenant in local part');
          throw new Error('Invalid localhost email: Missing tenant in local part');
        }
        console.log(`Extracted tenant from email local part: ${tenant}`);
        return tenant;
      }

      // Use the local part directly as the tenant (e.g., uog@localhost -> uog)
      if (localPart) {
        console.log(`Using local part as tenant: ${localPart}`);
        return localPart;
      }

      console.error('Invalid localhost email: Empty local part');
      throw new Error('Invalid localhost email: Empty local part');
    }

    // If we get here, we couldn't extract a tenant from the email
    console.error(`Could not extract tenant from email: ${email}`);
    throw new Error(`Could not extract tenant from email: ${email}`);
  };

  // Function to handle user login
  const login = useCallback(async (email, password, rememberMe = false) => {
    try {
      // Use the base URL directly for authentication
      const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
      console.log('Using base URL for authentication:', baseURL);

      // Extract tenant from email domain - this will throw an error if tenant can't be determined
      const emailTenant = extractTenantFromEmail(email);
      console.log('Tenant extracted from email domain:', emailTenant);

      // IMPORTANT: We ONLY use the tenant from the email domain
      // No fallbacks to URL parameters or default values
      const tenant = emailTenant;
      console.log('Using tenant for authentication:', tenant);

      // Store the tenant in localStorage for debugging
      localStorage.setItem('auth_tenant', tenant);
      localStorage.setItem('auth_email', email);

      // Get the JWT token with tenant header
      console.log('Sending authentication request with X-Tenant header:', tenant);
      const tokenResponse = await axios.post(`${baseURL}/api/v1/auth/token/`,
        {
          // Send both email and username fields to support both authentication methods
          email,
          username: email, // For backward compatibility with the backend
          password
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Tenant': tenant
          },
          withCredentials: true  // Important: receive cookies
        }
      );

      // The server will set the tokens as cookies
      // We'll also get them in the response for backward compatibility
      const { access } = tokenResponse.data;

      // Store UI-safe data from cookies in localStorage
      try {
        const uiDataCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('ui_data='))
          ?.split('=')[1];

        if (uiDataCookie) {
          const uiData = JSON.parse(decodeURIComponent(uiDataCookie));
          localStorage.setItem('ui_data', JSON.stringify(uiData));
        }
      } catch (e) {
        console.error('Error parsing UI data from cookie:', e);
      }

      // Then get the user data
      const userResponse = await api.get('/auth/user/');
      const userData = userResponse.data;

      // IMPORTANT: We ONLY use the tenant from the email domain
      // No fallbacks to UI data or other sources
      const finalTenant = emailTenant;

      // Log the tenant information for debugging
      console.log('Using tenant for user session:', finalTenant);
      console.log('Tenant source: email domain');

      // Verify that the tenant in the UI data matches the email tenant
      try {
        const uiData = JSON.parse(localStorage.getItem('ui_data') || '{}');
        if (uiData.tenant_id && uiData.tenant_id !== finalTenant) {
          console.warn(`Tenant mismatch: UI data tenant (${uiData.tenant_id}) does not match email tenant (${finalTenant})`);
          // We'll still use the email tenant, but log the mismatch for debugging
        }
      } catch (e) {
        console.error('Error parsing UI data:', e);
      }

      // Add tenant information to user data
      const enhancedUserData = {
        ...userData,
        tenant: finalTenant
      };

      // Set user in state
      setUser(enhancedUserData);

      // Signal that tenant has changed to trigger a refresh
      setTenantChanged(prev => !prev);

      // Store the final tenant in localStorage for debugging
      localStorage.setItem('final_tenant', finalTenant);

      // Dispatch a custom event to notify other components about tenant change
      window.dispatchEvent(new CustomEvent('tenant-changed', {
        detail: { tenant: finalTenant }
      }));

      // Set up session timeout
      setupSessionTimeout(access);

      // Set up activity tracking
      setupActivityTracking();

      return userData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, [setupActivityTracking, setupSessionTimeout]);

  // Create a ref to track if initialization is in progress
  const isInitializingRef = useRef(false);

  // Create a ref to track if we've already initialized
  const hasInitializedRef = useRef(false);

  // Create a ref for the auth check interval
  const authCheckIntervalRef = useRef(null);

  // Function to get user data and set up the session
  const setupUserSession = useCallback(async (uiDataStr) => {
    try {
      const userResponse = await api.get('/auth/user/');
      const userData = userResponse.data;

      // Get tenant information from UI data
      let tenant = null;
      if (uiDataStr) {
        try {
          const uiData = JSON.parse(uiDataStr);
          tenant = uiData.tenant_id;
        } catch (e) {
          console.error('Error parsing UI data:', e);
        }
      }

      // Add tenant information to user data
      const enhancedUserData = {
        ...userData,
        tenant: tenant
      };

      // Set user in state
      setUser(enhancedUserData);

      // Set up activity tracking
      setupActivityTracking();

      // Trigger tenant refresh if we have a tenant
      if (tenant) {
        window.dispatchEvent(new CustomEvent('tenant-changed', {
          detail: { tenant }
        }));
      }

      return true;
    } catch (error) {
      console.error('Error setting up user session:', error);
      return false;
    }
  }, [setupActivityTracking]);

  // Initialize auth state from cookies and localStorage - only runs once
  useEffect(() => {
    // Skip if we've already initialized
    if (hasInitializedRef.current) {
      return;
    }

    const initializeAuth = async () => {
      // Prevent multiple simultaneous initializations
      if (isInitializingRef.current) {
        return;
      }

      isInitializingRef.current = true;
      setLoading(true);
      console.log('Initializing authentication state...');

      try {
        // Check if we should prevent automatic login (after logout)
        const preventAutoLogin = localStorage.getItem('prevent_auto_login') === 'true';

        if (preventAutoLogin) {
          console.log('Preventing automatic login after logout');
          // Clear the flag
          localStorage.removeItem('prevent_auto_login');
          // Clear auth state
          clearAuthState();
          return;
        }

        // Try to get UI data from localStorage
        const uiDataStr = localStorage.getItem('ui_data');

        // Try to validate the current session by fetching user data
        try {
          const success = await setupUserSession(uiDataStr);
          if (success) {
            console.log('Valid session found, user session set up');
          }
        } catch (userError) {
          console.log('No valid session found or session expired');

          // Try to refresh the token
          try {
            const newToken = await refreshToken();

            if (newToken) {
              console.log('Token refresh successful, setting up user session');
              await setupUserSession(uiDataStr);
            } else {
              console.log('Token refresh failed, clearing auth data');
              clearAuthState();
            }
          } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
            clearAuthState();
          }
        }
      } catch (error) {
        console.error('Error initializing auth state:', error);
        clearAuthState();
      } finally {
        setLoading(false);
        isInitializingRef.current = false;
        hasInitializedRef.current = true;
      }
    };

    // Call initializeAuth immediately
    initializeAuth();

    // Clean up function
    return () => {
      // No cleanup needed for initial auth
    };
  }, [setupUserSession, refreshToken, clearAuthState]); // Include necessary dependencies, but will only run once due to hasInitializedRef check

  // Set up token refresh interval and event listeners
  useEffect(() => {
    // Skip if we're still initializing
    if (isInitializingRef.current) {
      return;
    }

    // Clear any existing interval
    if (authCheckIntervalRef.current) {
      clearInterval(authCheckIntervalRef.current);
    }

    // Set up a periodic check to refresh the token if needed
    authCheckIntervalRef.current = setInterval(() => {
      // Skip if initialization is in progress
      if (isInitializingRef.current) {
        return;
      }

      // Only refresh if we have a user
      if (user) {
        console.log('Periodic token refresh check...');
        refreshToken().catch(err => {
          console.error('Error during periodic token refresh:', err);
        });
      }
    }, 300000); // Check every 5 minutes

    // Add beforeunload event listener to handle session cleanup on tab/window close
    const handleBeforeUnload = () => {
      // With cookie-based auth, we don't need to do anything special here
      console.log('Window closing, session handled by cookies');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      // Clean up
      if (authCheckIntervalRef.current) {
        clearInterval(authCheckIntervalRef.current);
      }
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [user, refreshToken]); // Only re-run when user or refreshToken changes

  // Set up activity tracking
  useEffect(() => {
    // Skip if we're still initializing or no user
    if (isInitializingRef.current || !user) {
      return;
    }

    // Set up activity tracking
    const cleanup = setupActivityTracking();

    // Add activity event listeners
    window.addEventListener('mousemove', updateActivity);
    window.addEventListener('keydown', updateActivity);
    window.addEventListener('click', updateActivity);
    window.addEventListener('scroll', updateActivity);

    return () => {
      // Clean up
      if (cleanup) cleanup();
      window.removeEventListener('mousemove', updateActivity);
      window.removeEventListener('keydown', updateActivity);
      window.removeEventListener('click', updateActivity);
      window.removeEventListener('scroll', updateActivity);
    };
  }, [user, setupActivityTracking, updateActivity]);

  // Context value
  const value = {
    user,
    loading,
    sessionExpiring,
    login,
    logout,
    refreshToken,
    extendSession,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
