{"version": 3, "sources": ["../../@mui/x-charts/esm/PieChart/PieChart.js", "../../@mui/x-charts/esm/PieChart/PiePlot.js", "../../@mui/x-charts/esm/PieChart/PieArcPlot.js", "../../@mui/x-charts/esm/PieChart/PieArc.js", "../../@mui/x-charts/esm/PieChart/dataTransform/transition.js", "../../@mui/x-charts/esm/PieChart/dataTransform/useTransformData.js", "../../@mui/x-charts/esm/PieChart/PieArcLabelPlot.js", "../../@mui/x-charts/esm/PieChart/PieArcLabel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ResponsiveChartContainer } from '../ResponsiveChartContainer';\nimport { ChartsAxis } from '../ChartsAxis/ChartsAxis';\nimport { DEFAULT_X_AXIS_KEY } from '../constants';\nimport { ChartsTooltip } from '../ChartsTooltip';\nimport { ChartsLegend } from '../ChartsLegend';\nimport { ChartsAxisHighlight } from '../ChartsAxisHighlight';\nimport { PiePlot } from './PiePlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultMargin = {\n  top: 5,\n  bottom: 5,\n  left: 5,\n  right: 100\n};\n\n/**\n * Demos:\n *\n * - [Pie](https://mui.com/x/react-charts/pie/)\n * - [Pie demonstration](https://mui.com/x/react-charts/pie-demo/)\n *\n * API:\n *\n * - [PieChart API](https://mui.com/x/api/charts/pie-chart/)\n */\nfunction PieChart(props) {\n  const {\n    xAxis,\n    yAxis,\n    series,\n    width,\n    height,\n    margin: marginProps,\n    colors,\n    sx,\n    tooltip = {\n      trigger: 'item'\n    },\n    axisHighlight = {\n      x: 'none',\n      y: 'none'\n    },\n    skipAnimation,\n    legend = {\n      direction: 'column',\n      position: {\n        vertical: 'middle',\n        horizontal: 'right'\n      }\n    },\n    topAxis = null,\n    leftAxis = null,\n    rightAxis = null,\n    bottomAxis = null,\n    children,\n    slots,\n    slotProps,\n    onClick\n  } = props;\n  const margin = _extends({}, defaultMargin, marginProps);\n  return /*#__PURE__*/_jsxs(ResponsiveChartContainer, {\n    series: series.map(s => _extends({\n      type: 'pie'\n    }, s)),\n    width: width,\n    height: height,\n    margin: margin,\n    xAxis: xAxis != null ? xAxis : [{\n      id: DEFAULT_X_AXIS_KEY,\n      scaleType: 'point',\n      data: [...new Array(Math.max(...series.map(s => s.data.length)))].map((_, index) => index)\n    }],\n    yAxis: yAxis,\n    colors: colors,\n    sx: sx,\n    disableAxisListener: (tooltip == null ? void 0 : tooltip.trigger) !== 'axis' && (axisHighlight == null ? void 0 : axisHighlight.x) === 'none' && (axisHighlight == null ? void 0 : axisHighlight.y) === 'none',\n    children: [/*#__PURE__*/_jsx(ChartsAxis, {\n      topAxis: topAxis,\n      leftAxis: leftAxis,\n      rightAxis: rightAxis,\n      bottomAxis: bottomAxis,\n      slots: slots,\n      slotProps: slotProps\n    }), /*#__PURE__*/_jsx(PiePlot, {\n      slots: slots,\n      slotProps: slotProps,\n      onClick: onClick,\n      skipAnimation: skipAnimation\n    }), /*#__PURE__*/_jsx(ChartsLegend, _extends({}, legend, {\n      slots: slots,\n      slotProps: slotProps\n    })), /*#__PURE__*/_jsx(ChartsAxisHighlight, _extends({}, axisHighlight)), /*#__PURE__*/_jsx(ChartsTooltip, _extends({}, tooltip)), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? PieChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  axisHighlight: PropTypes.shape({\n    x: PropTypes.oneOf(['band', 'line', 'none']),\n    y: PropTypes.oneOf(['band', 'line', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the bottom of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default null\n   */\n  bottomAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default blueberryTwilightPalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * If `true`, the charts will not listen to the mouse move event.\n   * It might break interactive features, but will improve performance.\n   * @default false\n   */\n  disableAxisListener: PropTypes.bool,\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   * @default undefined\n   */\n  height: PropTypes.number,\n  /**\n   * Indicate which axis to display the left of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default null\n   */\n  leftAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  /**\n   * @deprecated Consider using `slotProps.legend` instead.\n   */\n  legend: PropTypes.shape({\n    classes: PropTypes.object,\n    direction: PropTypes.oneOf(['column', 'row']),\n    hidden: PropTypes.bool,\n    position: PropTypes.shape({\n      horizontal: PropTypes.oneOf(['left', 'middle', 'right']).isRequired,\n      vertical: PropTypes.oneOf(['bottom', 'middle', 'top']).isRequired\n    }),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object\n  }),\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   * @default object Depends on the charts type.\n   */\n  margin: PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  }),\n  onClick: PropTypes.func,\n  /**\n   * Indicate which axis to display the right of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsYAxisProps`.\n   * @default null\n   */\n  rightAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['left', 'right']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  series: PropTypes.arrayOf(PropTypes.shape({\n    arcLabel: PropTypes.oneOfType([PropTypes.oneOf(['formattedValue', 'label', 'value']), PropTypes.func]),\n    arcLabelMinAngle: PropTypes.number,\n    arcLabelRadius: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    cx: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    cy: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    data: PropTypes.arrayOf(PropTypes.shape({\n      color: PropTypes.string,\n      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n      label: PropTypes.string,\n      value: PropTypes.number.isRequired\n    })).isRequired,\n    endAngle: PropTypes.number,\n    faded: PropTypes.shape({\n      additionalRadius: PropTypes.number,\n      arcLabelRadius: PropTypes.number,\n      color: PropTypes.string,\n      cornerRadius: PropTypes.number,\n      innerRadius: PropTypes.number,\n      outerRadius: PropTypes.number,\n      paddingAngle: PropTypes.number\n    }),\n    highlighted: PropTypes.shape({\n      additionalRadius: PropTypes.number,\n      arcLabelRadius: PropTypes.number,\n      color: PropTypes.string,\n      cornerRadius: PropTypes.number,\n      innerRadius: PropTypes.number,\n      outerRadius: PropTypes.number,\n      paddingAngle: PropTypes.number\n    }),\n    highlightScope: PropTypes.shape({\n      faded: PropTypes.oneOf(['global', 'none', 'series']),\n      highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n    }),\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    innerRadius: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    outerRadius: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    paddingAngle: PropTypes.number,\n    sortingValues: PropTypes.oneOfType([PropTypes.oneOf(['asc', 'desc', 'none']), PropTypes.func]),\n    startAngle: PropTypes.number,\n    type: PropTypes.oneOf(['pie']),\n    valueFormatter: PropTypes.func\n  })).isRequired,\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  title: PropTypes.string,\n  tooltip: PropTypes.shape({\n    axisContent: PropTypes.elementType,\n    classes: PropTypes.object,\n    itemContent: PropTypes.elementType,\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    trigger: PropTypes.oneOf(['axis', 'item', 'none'])\n  }),\n  /**\n   * Indicate which axis to display the top of the charts.\n   * Can be a string (the id of the axis) or an object `ChartsXAxisProps`.\n   * @default null\n   */\n  topAxis: PropTypes.oneOfType([PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    position: PropTypes.oneOf(['bottom', 'top']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number\n  }), PropTypes.string]),\n  viewBox: PropTypes.shape({\n    height: PropTypes.number,\n    width: PropTypes.number,\n    x: PropTypes.number,\n    y: PropTypes.number\n  }),\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   * @default undefined\n   */\n  width: PropTypes.number,\n  /**\n   * The configuration of the x-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_X_AXIS_KEY`.\n   */\n  xAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  })),\n  /**\n   * The configuration of the y-axes.\n   * If not provided, a default axis config is used with id set to `DEFAULT_Y_AXIS_KEY`.\n   */\n  yAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    classes: PropTypes.object,\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    label: PropTypes.string,\n    labelFontSize: PropTypes.number,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    position: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band', 'linear', 'log', 'point', 'pow', 'sqrt', 'time', 'utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    tickFontSize: PropTypes.number,\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }))\n} : void 0;\nexport { PieChart };", "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { SeriesContext } from '../context/SeriesContextProvider';\nimport { DrawingContext } from '../context/DrawingProvider';\nimport { PieArcPlot } from './PieArcPlot';\nimport { PieArcLabelPlot } from './PieArcLabelPlot';\nimport { getPercentageValue } from '../internals/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Pie](https://mui.com/x/react-charts/pie/)\n * - [Pie demonstration](https://mui.com/x/react-charts/pie-demo/)\n *\n * API:\n *\n * - [PiePlot API](https://mui.com/x/api/charts/pie-plot/)\n */\nfunction PiePlot(props) {\n  const {\n    skipAnimation,\n    slots,\n    slotProps,\n    onClick\n  } = props;\n  const seriesData = React.useContext(SeriesContext).pie;\n  const {\n    left,\n    top,\n    width,\n    height\n  } = React.useContext(DrawingContext);\n  if (seriesData === undefined) {\n    return null;\n  }\n  const availableRadius = Math.min(width, height) / 2;\n  const {\n    series,\n    seriesOrder\n  } = seriesData;\n  return /*#__PURE__*/_jsxs(\"g\", {\n    children: [seriesOrder.map(seriesId => {\n      const {\n        innerRadius: innerRadiusParam,\n        outerRadius: outerRadiusParam,\n        cornerRadius,\n        paddingAngle,\n        data,\n        cx: cxParam,\n        cy: cyParam,\n        highlighted,\n        faded,\n        highlightScope\n      } = series[seriesId];\n      const outerRadius = getPercentageValue(outerRadiusParam != null ? outerRadiusParam : availableRadius, availableRadius);\n      const innerRadius = getPercentageValue(innerRadiusParam != null ? innerRadiusParam : 0, availableRadius);\n      const cx = getPercentageValue(cxParam != null ? cxParam : '50%', width);\n      const cy = getPercentageValue(cyParam != null ? cyParam : '50%', height);\n      return /*#__PURE__*/_jsx(\"g\", {\n        transform: `translate(${left + cx}, ${top + cy})`,\n        children: /*#__PURE__*/_jsx(PieArcPlot, {\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          cornerRadius: cornerRadius,\n          paddingAngle: paddingAngle,\n          id: seriesId,\n          data: data,\n          skipAnimation: skipAnimation,\n          highlightScope: highlightScope,\n          highlighted: highlighted,\n          faded: faded,\n          onClick: onClick,\n          slots: slots,\n          slotProps: slotProps\n        })\n      }, seriesId);\n    }), seriesOrder.map(seriesId => {\n      const {\n        innerRadius: innerRadiusParam,\n        outerRadius: outerRadiusParam,\n        arcLabelRadius: arcLabelRadiusParam,\n        cornerRadius,\n        paddingAngle,\n        arcLabel,\n        arcLabelMinAngle,\n        data,\n        cx: cxParam,\n        cy: cyParam,\n        highlightScope\n      } = series[seriesId];\n      const outerRadius = getPercentageValue(outerRadiusParam != null ? outerRadiusParam : availableRadius, availableRadius);\n      const innerRadius = getPercentageValue(innerRadiusParam != null ? innerRadiusParam : 0, availableRadius);\n      const arcLabelRadius = arcLabelRadiusParam === undefined ? (outerRadius + innerRadius) / 2 : getPercentageValue(arcLabelRadiusParam, availableRadius);\n      const cx = getPercentageValue(cxParam != null ? cxParam : '50%', width);\n      const cy = getPercentageValue(cyParam != null ? cyParam : '50%', height);\n      return /*#__PURE__*/_jsx(\"g\", {\n        transform: `translate(${left + cx}, ${top + cy})`,\n        children: /*#__PURE__*/_jsx(PieArcLabelPlot, {\n          innerRadius: innerRadius,\n          outerRadius: outerRadius != null ? outerRadius : availableRadius,\n          arcLabelRadius: arcLabelRadius,\n          cornerRadius: cornerRadius,\n          paddingAngle: paddingAngle,\n          id: seriesId,\n          data: data,\n          skipAnimation: skipAnimation,\n          arcLabel: arcLabel,\n          arcLabelMinAngle: arcLabelMinAngle,\n          highlightScope: highlightScope\n        })\n      }, seriesId);\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? PiePlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Callback fired when a pie item is clicked.\n   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.\n   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.\n   * @param {DefaultizedPieValueType} item The pie item.\n   */\n  onClick: PropTypes.func,\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PiePlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"slots\", \"slotProps\", \"innerRadius\", \"outerRadius\", \"cornerRadius\", \"paddingAngle\", \"id\", \"highlightScope\", \"highlighted\", \"faded\", \"data\", \"onClick\", \"skipAnimation\"],\n  _excluded2 = [\"startAngle\", \"endAngle\", \"paddingAngle\", \"innerRadius\", \"arcLabelRadius\", \"outerRadius\", \"cornerRadius\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTransition } from '@react-spring/web';\nimport { PieArc } from './PieArc';\nimport { defaultTransitionConfig } from './dataTransform/transition';\nimport { useTransformData } from './dataTransform/useTransformData';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction PieArcPlot(props) {\n  var _slots$pieArc;\n  const {\n      slots,\n      slotProps,\n      innerRadius = 0,\n      outerRadius,\n      cornerRadius = 0,\n      paddingAngle = 0,\n      id,\n      highlightScope,\n      highlighted,\n      faded = {\n        additionalRadius: -5\n      },\n      data,\n      onClick,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const transformedData = useTransformData({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    paddingAngle,\n    id,\n    highlightScope,\n    highlighted,\n    faded,\n    data\n  });\n  const transition = useTransition(transformedData, _extends({}, defaultTransitionConfig, {\n    immediate: skipAnimation\n  }));\n  if (data.length === 0) {\n    return null;\n  }\n  const Arc = (_slots$pieArc = slots == null ? void 0 : slots.pieArc) != null ? _slots$pieArc : PieArc;\n  return /*#__PURE__*/_jsx(\"g\", _extends({}, other, {\n    children: transition((_ref, item, _, index) => {\n      let {\n          startAngle,\n          endAngle,\n          paddingAngle: pA,\n          innerRadius: iR,\n          outerRadius: oR,\n          cornerRadius: cR\n        } = _ref,\n        style = _objectWithoutPropertiesLoose(_ref, _excluded2);\n      return /*#__PURE__*/_jsx(Arc, _extends({\n        startAngle: startAngle,\n        endAngle: endAngle,\n        paddingAngle: pA,\n        innerRadius: iR,\n        outerRadius: oR,\n        cornerRadius: cR,\n        style: style,\n        id: id,\n        color: item.color,\n        dataIndex: index,\n        highlightScope: highlightScope,\n        isFaded: item.isFaded,\n        isHighlighted: item.isHighlighted,\n        onClick: onClick && (event => {\n          onClick(event, {\n            type: 'pie',\n            seriesId: id,\n            dataIndex: index\n          }, item);\n        })\n      }, slotProps == null ? void 0 : slotProps.pieArc));\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArcPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The radius between circle center and the arc label in px.\n   * @default (innerRadius - outerRadius) / 2\n   */\n  arcLabelRadius: PropTypes.number,\n  /**\n   * The radius applied to arc corners (similar to border radius).\n   * @default 0\n   */\n  cornerRadius: PropTypes.number,\n  data: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string.isRequired,\n    endAngle: PropTypes.number.isRequired,\n    formattedValue: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n    index: PropTypes.number.isRequired,\n    label: PropTypes.string,\n    padAngle: PropTypes.number.isRequired,\n    startAngle: PropTypes.number.isRequired,\n    value: PropTypes.number.isRequired\n  })).isRequired,\n  /**\n   * Override the arc attibutes when it is faded.\n   * @default { additionalRadius: -5 }\n   */\n  faded: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * Override the arc attibutes when it is highlighted.\n   */\n  highlighted: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  highlightScope: PropTypes.shape({\n    faded: PropTypes.oneOf(['global', 'none', 'series']),\n    highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n  }),\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The radius between circle center and the begining of the arc.\n   * @default 0\n   */\n  innerRadius: PropTypes.number,\n  /**\n   * Callback fired when a pie item is clicked.\n   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.\n   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.\n   * @param {DefaultizedPieValueType} item The pie item.\n   */\n  onClick: PropTypes.func,\n  /**\n   * The radius between circle center and the end of the arc.\n   */\n  outerRadius: PropTypes.number.isRequired,\n  /**\n   * The padding angle (deg) between two arcs.\n   * @default 0\n   */\n  paddingAngle: PropTypes.number,\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PieArcPlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"dataIndex\", \"classes\", \"color\", \"highlightScope\", \"onClick\", \"isFaded\", \"isHighlighted\", \"startAngle\", \"endAngle\", \"paddingAngle\", \"innerRadius\", \"outerRadius\", \"cornerRadius\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { arc as d3Arc } from 'd3-shape';\nimport { animated, to } from '@react-spring/web';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport { useInteractionItemProps } from '../hooks/useInteractionItemProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getPieArcUtilityClass(slot) {\n  return generateUtilityClass('MuiPieArc', slot);\n}\nexport const pieArcClasses = generateUtilityClasses('MuiPieArc', ['root', 'highlighted', 'faded']);\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id,\n    isFaded,\n    isHighlighted\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`, isHighlighted && 'highlighted', isFaded && 'faded']\n  };\n  return composeClasses(slots, getPieArcUtilityClass, classes);\n};\nconst PieArcRoot = styled(animated.path, {\n  name: 'MuiPieArc',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.arc\n})(({\n  theme\n}) => ({\n  stroke: (theme.vars || theme).palette.background.paper,\n  strokeWidth: 1,\n  strokeLinejoin: 'round'\n}));\nfunction PieArc(props) {\n  const {\n      id,\n      dataIndex,\n      classes: innerClasses,\n      color,\n      highlightScope,\n      onClick,\n      isFaded,\n      isHighlighted,\n      startAngle,\n      endAngle,\n      paddingAngle,\n      innerRadius,\n      outerRadius,\n      cornerRadius\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    id,\n    dataIndex,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted\n  };\n  const classes = useUtilityClasses(ownerState);\n  const getInteractionItemProps = useInteractionItemProps(highlightScope);\n  return /*#__PURE__*/_jsx(PieArcRoot, _extends({\n    d: to([startAngle, endAngle, paddingAngle, innerRadius, outerRadius, cornerRadius], (sA, eA, pA, iR, oR, cR) => d3Arc().cornerRadius(cR)({\n      padAngle: pA,\n      startAngle: sA,\n      endAngle: eA,\n      innerRadius: iR,\n      outerRadius: oR\n    })),\n    onClick: onClick,\n    cursor: onClick ? 'pointer' : 'unset',\n    ownerState: ownerState,\n    className: classes.root\n  }, other, getInteractionItemProps({\n    type: 'pie',\n    seriesId: id,\n    dataIndex\n  })));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArc.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  dataIndex: PropTypes.number.isRequired,\n  highlightScope: PropTypes.shape({\n    faded: PropTypes.oneOf(['global', 'none', 'series']),\n    highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n  }),\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  isFaded: PropTypes.bool.isRequired,\n  isHighlighted: PropTypes.bool.isRequired\n} : void 0;\nexport { PieArc };", "export const defaultTransitionConfig = {\n  keys: item => item.id,\n  from: ({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle,\n    color,\n    isFaded\n  }) => ({\n    innerRadius,\n    outerRadius: (innerRadius + outerRadius) / 2,\n    cornerRadius,\n    startAngle: (startAngle + endAngle) / 2,\n    endAngle: (startAngle + endAngle) / 2,\n    paddingAngle,\n    fill: color,\n    opacity: isFaded ? 0.3 : 1\n  }),\n  leave: ({\n    innerRadius,\n    startAngle,\n    endAngle\n  }) => ({\n    innerRadius,\n    outerRadius: innerRadius,\n    startAngle: (startAngle + endAngle) / 2,\n    endAngle: (startAngle + endAngle) / 2\n  }),\n  enter: ({\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  }) => ({\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  }),\n  update: ({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle,\n    color,\n    isFaded\n  }) => ({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle,\n    fill: color,\n    opacity: isFaded ? 0.3 : 1\n  }),\n  config: {\n    tension: 120,\n    friction: 14,\n    clamp: true\n  }\n};\nexport const defaultLabelTransitionConfig = {\n  keys: item => item.id,\n  from: ({\n    innerRadius,\n    outerRadius,\n    arcLabelRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle\n  }) => ({\n    innerRadius,\n    outerRadius: (innerRadius + outerRadius) / 2,\n    cornerRadius,\n    arcLabelRadius,\n    startAngle: (startAngle + endAngle) / 2,\n    endAngle: (startAngle + endAngle) / 2,\n    paddingAngle,\n    opacity: 0\n  }),\n  leave: ({\n    innerRadius,\n    startAngle,\n    endAngle\n  }) => ({\n    innerRadius,\n    outerRadius: innerRadius,\n    arcLabelRadius: innerRadius,\n    startAngle: (startAngle + endAngle) / 2,\n    endAngle: (startAngle + endAngle) / 2,\n    opacity: 0\n  }),\n  enter: ({\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle,\n    arcLabelRadius\n  }) => ({\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle,\n    arcLabelRadius,\n    opacity: 1\n  }),\n  update: ({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle,\n    arcLabelRadius\n  }) => ({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    startAngle,\n    endAngle,\n    paddingAngle,\n    arcLabelRadius,\n    opacity: 1\n  }),\n  config: {\n    tension: 120,\n    friction: 14,\n    clamp: true\n  }\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { InteractionContext } from '../../context/InteractionProvider';\nimport { getIsHighlighted, getIsFaded } from '../../hooks/useInteractionItemProps';\nexport function useTransformData(series) {\n  const {\n    id: seriesId,\n    highlightScope,\n    data,\n    faded,\n    highlighted,\n    paddingAngle: basePaddingAngle = 0,\n    innerRadius: baseInnerRadius = 0,\n    arcLabelRadius: baseArcLabelRadius,\n    outerRadius: baseOuterRadius,\n    cornerRadius: baseCornerRadius = 0\n  } = series;\n  const {\n    item: highlightedItem\n  } = React.useContext(InteractionContext);\n  const getHighlightStatus = React.useCallback(dataIndex => {\n    const isHighlighted = getIsHighlighted(highlightedItem, {\n      type: 'pie',\n      seriesId,\n      dataIndex\n    }, highlightScope);\n    const isFaded = !isHighlighted && getIsFaded(highlightedItem, {\n      type: 'pie',\n      seriesId,\n      dataIndex\n    }, highlightScope);\n    return {\n      isHighlighted,\n      isFaded\n    };\n  }, [highlightScope, highlightedItem, seriesId]);\n  const dataWithHighlight = React.useMemo(() => data.map((item, itemIndex) => {\n    var _attributesOverride$p, _attributesOverride$i, _attributesOverride$o, _attributesOverride$c, _ref, _attributesOverride$a;\n    const {\n      isHighlighted,\n      isFaded\n    } = getHighlightStatus(itemIndex);\n    const attributesOverride = _extends({\n      additionalRadius: 0\n    }, isFaded && faded || isHighlighted && highlighted || {});\n    const paddingAngle = Math.max(0, Math.PI * ((_attributesOverride$p = attributesOverride.paddingAngle) != null ? _attributesOverride$p : basePaddingAngle) / 180);\n    const innerRadius = Math.max(0, (_attributesOverride$i = attributesOverride.innerRadius) != null ? _attributesOverride$i : baseInnerRadius);\n    const outerRadius = Math.max(0, (_attributesOverride$o = attributesOverride.outerRadius) != null ? _attributesOverride$o : baseOuterRadius + attributesOverride.additionalRadius);\n    const cornerRadius = (_attributesOverride$c = attributesOverride.cornerRadius) != null ? _attributesOverride$c : baseCornerRadius;\n    const arcLabelRadius = (_ref = (_attributesOverride$a = attributesOverride.arcLabelRadius) != null ? _attributesOverride$a : baseArcLabelRadius) != null ? _ref : (innerRadius + outerRadius) / 2;\n    return _extends({}, item, attributesOverride, {\n      isFaded,\n      isHighlighted,\n      paddingAngle,\n      innerRadius,\n      outerRadius,\n      cornerRadius,\n      arcLabelRadius\n    });\n  }), [baseCornerRadius, baseInnerRadius, baseOuterRadius, basePaddingAngle, baseArcLabelRadius, data, faded, getHighlightStatus, highlighted]);\n  return dataWithHighlight;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"slots\", \"slotProps\", \"innerRadius\", \"outerRadius\", \"arcLabelRadius\", \"cornerRadius\", \"paddingAngle\", \"id\", \"highlightScope\", \"highlighted\", \"faded\", \"data\", \"arcLabel\", \"arcLabelMinAngle\", \"skipAnimation\"],\n  _excluded2 = [\"startAngle\", \"endAngle\", \"paddingAngle\", \"innerRadius\", \"outerRadius\", \"arcLabelRadius\", \"cornerRadius\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTransition } from '@react-spring/web';\nimport { defaultLabelTransitionConfig } from './dataTransform/transition';\nimport { useTransformData } from './dataTransform/useTransformData';\nimport { PieArcLabel } from './PieArcLabel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RATIO = 180 / Math.PI;\nfunction getItemLabel(arcLabel, arcLabelMinAngle, item) {\n  if (!arcLabel) {\n    return null;\n  }\n  const angle = (item.endAngle - item.startAngle) * RATIO;\n  if (angle < arcLabelMinAngle) {\n    return null;\n  }\n  if (typeof arcLabel === 'string') {\n    var _item$arcLabel;\n    return (_item$arcLabel = item[arcLabel]) == null ? void 0 : _item$arcLabel.toString();\n  }\n  return arcLabel(item);\n}\nfunction PieArcLabelPlot(props) {\n  var _slots$pieArcLabel;\n  const {\n      slots,\n      slotProps,\n      innerRadius,\n      outerRadius,\n      arcLabelRadius,\n      cornerRadius = 0,\n      paddingAngle = 0,\n      id,\n      highlightScope,\n      highlighted,\n      faded = {\n        additionalRadius: -5\n      },\n      data,\n      arcLabel,\n      arcLabelMinAngle = 0,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const transformedData = useTransformData({\n    innerRadius,\n    outerRadius,\n    arcLabelRadius,\n    cornerRadius,\n    paddingAngle,\n    id,\n    highlightScope,\n    highlighted,\n    faded,\n    data\n  });\n  const transition = useTransition(transformedData, _extends({}, defaultLabelTransitionConfig, {\n    immediate: skipAnimation\n  }));\n  if (data.length === 0) {\n    return null;\n  }\n  const ArcLabel = (_slots$pieArcLabel = slots == null ? void 0 : slots.pieArcLabel) != null ? _slots$pieArcLabel : PieArcLabel;\n  return /*#__PURE__*/_jsx(\"g\", _extends({}, other, {\n    children: transition((_ref, item) => {\n      let {\n          startAngle,\n          endAngle,\n          paddingAngle: pA,\n          innerRadius: iR,\n          outerRadius: oR,\n          arcLabelRadius: aLR,\n          cornerRadius: cR\n        } = _ref,\n        style = _objectWithoutPropertiesLoose(_ref, _excluded2);\n      return /*#__PURE__*/_jsx(ArcLabel, _extends({\n        startAngle: startAngle,\n        endAngle: endAngle,\n        paddingAngle: pA,\n        innerRadius: iR,\n        outerRadius: oR,\n        arcLabelRadius: aLR,\n        cornerRadius: cR,\n        style: style,\n        id: id,\n        color: item.color,\n        isFaded: item.isFaded,\n        isHighlighted: item.isHighlighted,\n        formattedArcLabel: getItemLabel(arcLabel, arcLabelMinAngle, item)\n      }, slotProps == null ? void 0 : slotProps.pieArcLabel));\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArcLabelPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The label displayed into the arc.\n   */\n  arcLabel: PropTypes.oneOfType([PropTypes.oneOf(['formattedValue', 'label', 'value']), PropTypes.func]),\n  /**\n   * The minimal angle required to display the arc label.\n   * @default 0\n   */\n  arcLabelMinAngle: PropTypes.number,\n  /**\n   * The radius between circle center and the arc label in px.\n   * @default (innerRadius - outerRadius) / 2\n   */\n  arcLabelRadius: PropTypes.number,\n  /**\n   * The radius applied to arc corners (similar to border radius).\n   * @default 0\n   */\n  cornerRadius: PropTypes.number,\n  data: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string.isRequired,\n    endAngle: PropTypes.number.isRequired,\n    formattedValue: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n    index: PropTypes.number.isRequired,\n    label: PropTypes.string,\n    padAngle: PropTypes.number.isRequired,\n    startAngle: PropTypes.number.isRequired,\n    value: PropTypes.number.isRequired\n  })).isRequired,\n  /**\n   * Override the arc attibutes when it is faded.\n   * @default { additionalRadius: -5 }\n   */\n  faded: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * Override the arc attibutes when it is highlighted.\n   */\n  highlighted: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  highlightScope: PropTypes.shape({\n    faded: PropTypes.oneOf(['global', 'none', 'series']),\n    highlighted: PropTypes.oneOf(['item', 'none', 'series'])\n  }),\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The radius between circle center and the begining of the arc.\n   * @default 0\n   */\n  innerRadius: PropTypes.number,\n  /**\n   * The radius between circle center and the end of the arc.\n   */\n  outerRadius: PropTypes.number.isRequired,\n  /**\n   * The padding angle (deg) between two arcs.\n   * @default 0\n   */\n  paddingAngle: PropTypes.number,\n  /**\n   * If `true`, animations are skiped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PieArcLabelPlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"classes\", \"color\", \"startAngle\", \"endAngle\", \"paddingAngle\", \"arcLabelRadius\", \"innerRadius\", \"outerRadius\", \"cornerRadius\", \"formattedArcLabel\", \"isHighlighted\", \"isFaded\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { animated, to } from '@react-spring/web';\nimport { arc as d3Arc } from 'd3-shape';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getPieArcLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiPieArcLabel', slot);\n}\nexport const pieArcLabelClasses = generateUtilityClasses('MuiPieArcLabel', ['root', 'highlighted', 'faded']);\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id,\n    isFaded,\n    isHighlighted\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`, isHighlighted && 'highlighted', isFaded && 'faded']\n  };\n  return composeClasses(slots, getPieArcLabelUtilityClass, classes);\n};\nconst PieArcLabelRoot = styled(animated.text, {\n  name: 'MuiPieArcLabel',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  theme\n}) => ({\n  fill: (theme.vars || theme).palette.text.primary,\n  textAnchor: 'middle',\n  dominantBaseline: 'middle'\n}));\n/**\n * Helper to compute label position.\n * It's not an inline function because we need it in inerpolation.\n */\nconst getLabelPosition = (formattedArcLabel, variable) => (startAngle, endAngle, padAngle, arcLabelRadius, cornerRadius) => {\n  if (!formattedArcLabel) {\n    return 0;\n  }\n  const [x, y] = d3Arc().cornerRadius(cornerRadius).centroid({\n    padAngle,\n    startAngle,\n    endAngle,\n    innerRadius: arcLabelRadius,\n    outerRadius: arcLabelRadius\n  });\n  if (variable === 'x') {\n    return x;\n  }\n  return y;\n};\nfunction PieArcLabel(props) {\n  const {\n      id,\n      classes: innerClasses,\n      color,\n      startAngle,\n      endAngle,\n      paddingAngle,\n      arcLabelRadius,\n      cornerRadius,\n      formattedArcLabel,\n      isHighlighted,\n      isFaded,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    id,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(PieArcLabelRoot, _extends({\n    className: classes.root\n  }, other, {\n    style: _extends({\n      x: to([startAngle, endAngle, paddingAngle, arcLabelRadius, cornerRadius], getLabelPosition(formattedArcLabel, 'x')),\n      y: to([startAngle, endAngle, paddingAngle, arcLabelRadius, cornerRadius], getLabelPosition(formattedArcLabel, 'y'))\n    }, style),\n    children: formattedArcLabel\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArcLabel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  formattedArcLabel: PropTypes.string,\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  isFaded: PropTypes.bool.isRequired,\n  isHighlighted: PropTypes.bool.isRequired\n} : void 0;\nexport { PieArcLabel };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,IAAAA,SAAuB;AACvB,IAAAC,qBAAsB;;;ACFtB,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACDtB;AACA;AAGA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACLtB;AACA;AAEA,YAAuB;AACvB,wBAAsB;AAGtB;AACA;AAEA;AAEA,yBAA4B;AAV5B,IAAM,YAAY,CAAC,MAAM,aAAa,WAAW,SAAS,kBAAkB,WAAW,WAAW,iBAAiB,cAAc,YAAY,gBAAgB,eAAe,eAAe,cAAc;AAWlM,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACO,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,eAAe,OAAO,CAAC;AACjG,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,IAAI,iBAAiB,eAAe,WAAW,OAAO;AAAA,EACnF;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AACA,IAAM,aAAa,eAAO,SAAS,MAAM;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,GAAG,WAAW,OAAO;AAC3C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,SAAS,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EACjD,aAAa;AAAA,EACb,gBAAgB;AAClB,EAAE;AACF,SAAS,OAAO,OAAO;AACrB,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,0BAA0B,wBAAwB,cAAc;AACtE,aAAoB,mBAAAC,KAAK,YAAY,SAAS;AAAA,IAC5C,GAAG,GAAG,CAAC,YAAY,UAAU,cAAc,aAAa,aAAa,YAAY,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,YAAM,EAAE,aAAa,EAAE,EAAE;AAAA,MACvI,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF;AAAA,IACA,QAAQ,UAAU,YAAY;AAAA,IAC9B;AAAA,IACA,WAAW,QAAQ;AAAA,EACrB,GAAG,OAAO,wBAAwB;AAAA,IAChC,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF,CAAC,CAAC,CAAC;AACL;AACA,OAAwC,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzD,SAAS,kBAAAC,QAAU;AAAA,EACnB,WAAW,kBAAAA,QAAU,OAAO;AAAA,EAC5B,gBAAgB,kBAAAA,QAAU,MAAM;AAAA,IAC9B,OAAO,kBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,IACnD,aAAa,kBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzD,CAAC;AAAA,EACD,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAC9D,SAAS,kBAAAA,QAAU,KAAK;AAAA,EACxB,eAAe,kBAAAA,QAAU,KAAK;AAChC,IAAI;;;ACpGG,IAAM,0BAA0B;AAAA,EACrC,MAAM,UAAQ,KAAK;AAAA,EACnB,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA,cAAc,cAAc,eAAe;AAAA,IAC3C;AAAA,IACA,aAAa,aAAa,YAAY;AAAA,IACtC,WAAW,aAAa,YAAY;AAAA,IACpC;AAAA,IACA,MAAM;AAAA,IACN,SAAS,UAAU,MAAM;AAAA,EAC3B;AAAA,EACA,OAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA,aAAa;AAAA,IACb,aAAa,aAAa,YAAY;AAAA,IACtC,WAAW,aAAa,YAAY;AAAA,EACtC;AAAA,EACA,OAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,QAAQ,CAAC;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,SAAS,UAAU,MAAM;AAAA,EAC3B;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACF;AACO,IAAM,+BAA+B;AAAA,EAC1C,MAAM,UAAQ,KAAK;AAAA,EACnB,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA,cAAc,cAAc,eAAe;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,aAAa,aAAa,YAAY;AAAA,IACtC,WAAW,aAAa,YAAY;AAAA,IACpC;AAAA,IACA,SAAS;AAAA,EACX;AAAA,EACA,OAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,aAAa,aAAa,YAAY;AAAA,IACtC,WAAW,aAAa,YAAY;AAAA,IACpC,SAAS;AAAA,EACX;AAAA,EACA,OAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX;AAAA,EACA,QAAQ,CAAC;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AACF;;;ACxIA;AACA,IAAAC,SAAuB;AAGhB,SAAS,iBAAiB,QAAQ;AACvC,QAAM;AAAA,IACJ,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,mBAAmB;AAAA,IACjC,aAAa,kBAAkB;AAAA,IAC/B,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc,mBAAmB;AAAA,EACnC,IAAI;AACJ,QAAM;AAAA,IACJ,MAAM;AAAA,EACR,IAAU,kBAAW,kBAAkB;AACvC,QAAM,qBAA2B,mBAAY,eAAa;AACxD,UAAM,gBAAgB,iBAAiB,iBAAiB;AAAA,MACtD,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,GAAG,cAAc;AACjB,UAAM,UAAU,CAAC,iBAAiB,WAAW,iBAAiB;AAAA,MAC5D,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,GAAG,cAAc;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,gBAAgB,iBAAiB,QAAQ,CAAC;AAC9C,QAAM,oBAA0B,eAAQ,MAAM,KAAK,IAAI,CAAC,MAAM,cAAc;AAC1E,QAAI,uBAAuB,uBAAuB,uBAAuB,uBAAuB,MAAM;AACtG,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,mBAAmB,SAAS;AAChC,UAAM,qBAAqB,SAAS;AAAA,MAClC,kBAAkB;AAAA,IACpB,GAAG,WAAW,SAAS,iBAAiB,eAAe,CAAC,CAAC;AACzD,UAAM,eAAe,KAAK,IAAI,GAAG,KAAK,OAAO,wBAAwB,mBAAmB,iBAAiB,OAAO,wBAAwB,oBAAoB,GAAG;AAC/J,UAAM,cAAc,KAAK,IAAI,IAAI,wBAAwB,mBAAmB,gBAAgB,OAAO,wBAAwB,eAAe;AAC1I,UAAM,cAAc,KAAK,IAAI,IAAI,wBAAwB,mBAAmB,gBAAgB,OAAO,wBAAwB,kBAAkB,mBAAmB,gBAAgB;AAChL,UAAM,gBAAgB,wBAAwB,mBAAmB,iBAAiB,OAAO,wBAAwB;AACjH,UAAM,kBAAkB,QAAQ,wBAAwB,mBAAmB,mBAAmB,OAAO,wBAAwB,uBAAuB,OAAO,QAAQ,cAAc,eAAe;AAChM,WAAO,SAAS,CAAC,GAAG,MAAM,oBAAoB;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,CAAC,kBAAkB,iBAAiB,iBAAiB,kBAAkB,oBAAoB,MAAM,OAAO,oBAAoB,WAAW,CAAC;AAC5I,SAAO;AACT;;;AHnDA,IAAAC,sBAA4B;AAR5B,IAAMC,aAAY,CAAC,SAAS,aAAa,eAAe,eAAe,gBAAgB,gBAAgB,MAAM,kBAAkB,eAAe,SAAS,QAAQ,WAAW,eAAe;AAAzL,IACEC,cAAa,CAAC,cAAc,YAAY,gBAAgB,eAAe,kBAAkB,eAAe,cAAc;AAQxH,SAAS,WAAW,OAAO;AACzB,MAAI;AACJ,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,kBAAkB,iBAAiB;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa,cAAc,iBAAiB,SAAS,CAAC,GAAG,yBAAyB;AAAA,IACtF,WAAW;AAAA,EACb,CAAC,CAAC;AACF,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,gBAAgB,SAAS,OAAO,SAAS,MAAM,WAAW,OAAO,gBAAgB;AAC9F,aAAoB,oBAAAE,KAAK,KAAK,SAAS,CAAC,GAAG,OAAO;AAAA,IAChD,UAAU,WAAW,CAAC,MAAM,MAAM,GAAG,UAAU;AAC7C,UAAI;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,MAChB,IAAI,MACJ,QAAQ,8BAA8B,MAAMD,WAAU;AACxD,iBAAoB,oBAAAC,KAAK,KAAK,SAAS;AAAA,QACrC;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QACd;AAAA,QACA;AAAA,QACA,OAAO,KAAK;AAAA,QACZ,WAAW;AAAA,QACX;AAAA,QACA,SAAS,KAAK;AAAA,QACd,eAAe,KAAK;AAAA,QACpB,SAAS,YAAY,WAAS;AAC5B,kBAAQ,OAAO;AAAA,YACb,MAAM;AAAA,YACN,UAAU;AAAA,YACV,WAAW;AAAA,UACb,GAAG,IAAI;AAAA,QACT;AAAA,MACF,GAAG,aAAa,OAAO,SAAS,UAAU,MAAM,CAAC;AAAA,IACnD,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7D,gBAAgB,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,cAAc,mBAAAA,QAAU;AAAA,EACxB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtC,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,gBAAgB,mBAAAA,QAAU,OAAO;AAAA,IACjC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAC9D,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,mBAAAA,QAAU;AAAA,IACjB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,YAAY,mBAAAA,QAAU,OAAO;AAAA,IAC7B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,aAAa,mBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA,EACD,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,IAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,IACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzD,CAAC;AAAA,EACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,aAAa,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AIjLJ;AACA;AAGA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACLtB;AACA;AAEA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AAGtB;AACA;AAEA;AACA,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,MAAM,WAAW,SAAS,cAAc,YAAY,gBAAgB,kBAAkB,eAAe,eAAe,gBAAgB,qBAAqB,iBAAiB,WAAW,OAAO;AAUxM,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACO,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,QAAQ,eAAe,OAAO,CAAC;AAC3G,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,IAAI,iBAAiB,eAAe,WAAW,OAAO;AAAA,EACnF;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACA,IAAM,kBAAkB,eAAO,SAAS,MAAM;AAAA,EAC5C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,GAAG,WAAW,OAAO;AAC3C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EACzC,YAAY;AAAA,EACZ,kBAAkB;AACpB,EAAE;AAKF,IAAM,mBAAmB,CAAC,mBAAmB,aAAa,CAAC,YAAY,UAAU,UAAU,gBAAgB,iBAAiB;AAC1H,MAAI,CAAC,mBAAmB;AACtB,WAAO;AAAA,EACT;AACA,QAAM,CAAC,GAAG,CAAC,IAAI,YAAM,EAAE,aAAa,YAAY,EAAE,SAAS;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,aAAa;AAAA,EACf,CAAC;AACD,MAAI,aAAa,KAAK;AACpB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAUC,mBAAkB,UAAU;AAC5C,aAAoB,oBAAAC,KAAK,iBAAiB,SAAS;AAAA,IACjD,WAAW,QAAQ;AAAA,EACrB,GAAG,OAAO;AAAA,IACR,OAAO,SAAS;AAAA,MACd,GAAG,GAAG,CAAC,YAAY,UAAU,cAAc,gBAAgB,YAAY,GAAG,iBAAiB,mBAAmB,GAAG,CAAC;AAAA,MAClH,GAAG,GAAG,CAAC,YAAY,UAAU,cAAc,gBAAgB,YAAY,GAAG,iBAAiB,mBAAmB,GAAG,CAAC;AAAA,IACpH,GAAG,KAAK;AAAA,IACR,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,OAAwC,YAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,SAAS,mBAAAC,QAAU;AAAA,EACnB,mBAAmB,mBAAAA,QAAU;AAAA,EAC7B,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAC9D,SAAS,mBAAAA,QAAU,KAAK;AAAA,EACxB,eAAe,mBAAAA,QAAU,KAAK;AAChC,IAAI;;;AD7FJ,IAAAC,sBAA4B;AAR5B,IAAMC,aAAY,CAAC,SAAS,aAAa,eAAe,eAAe,kBAAkB,gBAAgB,gBAAgB,MAAM,kBAAkB,eAAe,SAAS,QAAQ,YAAY,oBAAoB,eAAe;AAAhO,IACEC,cAAa,CAAC,cAAc,YAAY,gBAAgB,eAAe,eAAe,kBAAkB,cAAc;AAQxH,IAAM,QAAQ,MAAM,KAAK;AACzB,SAAS,aAAa,UAAU,kBAAkB,MAAM;AACtD,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,WAAW,KAAK,cAAc;AAClD,MAAI,QAAQ,kBAAkB;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,QAAI;AACJ,YAAQ,iBAAiB,KAAK,QAAQ,MAAM,OAAO,SAAS,eAAe,SAAS;AAAA,EACtF;AACA,SAAO,SAAS,IAAI;AACtB;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI;AACJ,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,kBAAkB,iBAAiB;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa,cAAc,iBAAiB,SAAS,CAAC,GAAG,8BAA8B;AAAA,IAC3F,WAAW;AAAA,EACb,CAAC,CAAC;AACF,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,qBAAqB,SAAS,OAAO,SAAS,MAAM,gBAAgB,OAAO,qBAAqB;AAClH,aAAoB,oBAAAE,KAAK,KAAK,SAAS,CAAC,GAAG,OAAO;AAAA,IAChD,UAAU,WAAW,CAAC,MAAM,SAAS;AACnC,UAAI;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB,IAAI,MACJ,QAAQ,8BAA8B,MAAMD,WAAU;AACxD,iBAAoB,oBAAAC,KAAK,UAAU,SAAS;AAAA,QAC1C;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd;AAAA,QACA;AAAA,QACA,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,eAAe,KAAK;AAAA,QACpB,mBAAmB,aAAa,UAAU,kBAAkB,IAAI;AAAA,MAClE,GAAG,aAAa,OAAO,SAAS,UAAU,WAAW,CAAC;AAAA,IACxD,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AACA,OAAwC,gBAAgB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlE,UAAU,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,kBAAkB,SAAS,OAAO,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrG,kBAAkB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,cAAc,mBAAAA,QAAU;AAAA,EACxB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtC,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,gBAAgB,mBAAAA,QAAU,OAAO;AAAA,IACjC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAC9D,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,mBAAAA,QAAU;AAAA,IACjB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,YAAY,mBAAAA,QAAU,OAAO;AAAA,IAC7B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,aAAa,mBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA,EACD,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,IAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,IACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,EACzD,CAAC;AAAA,EACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ALxLJ,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAW9B,SAAS,QAAQ,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAmB,kBAAW,aAAa,EAAE;AACnD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAU,kBAAW,cAAc;AACnC,MAAI,eAAe,QAAW;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,KAAK,IAAI,OAAO,MAAM,IAAI;AAClD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,aAAoB,oBAAAC,MAAM,KAAK;AAAA,IAC7B,UAAU,CAAC,YAAY,IAAI,cAAY;AACrC,YAAM;AAAA,QACJ,aAAa;AAAA,QACb,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,YAAM,cAAc,mBAAmB,oBAAoB,OAAO,mBAAmB,iBAAiB,eAAe;AACrH,YAAM,cAAc,mBAAmB,oBAAoB,OAAO,mBAAmB,GAAG,eAAe;AACvG,YAAM,KAAK,mBAAmB,WAAW,OAAO,UAAU,OAAO,KAAK;AACtE,YAAM,KAAK,mBAAmB,WAAW,OAAO,UAAU,OAAO,MAAM;AACvE,iBAAoB,oBAAAC,KAAK,KAAK;AAAA,QAC5B,WAAW,aAAa,OAAO,EAAE,KAAK,MAAM,EAAE;AAAA,QAC9C,cAAuB,oBAAAA,KAAK,YAAY;AAAA,UACtC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC,GAAG,YAAY,IAAI,cAAY;AAC9B,YAAM;AAAA,QACJ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,YAAM,cAAc,mBAAmB,oBAAoB,OAAO,mBAAmB,iBAAiB,eAAe;AACrH,YAAM,cAAc,mBAAmB,oBAAoB,OAAO,mBAAmB,GAAG,eAAe;AACvG,YAAM,iBAAiB,wBAAwB,UAAa,cAAc,eAAe,IAAI,mBAAmB,qBAAqB,eAAe;AACpJ,YAAM,KAAK,mBAAmB,WAAW,OAAO,UAAU,OAAO,KAAK;AACtE,YAAM,KAAK,mBAAmB,WAAW,OAAO,UAAU,OAAO,MAAM;AACvE,iBAAoB,oBAAAA,KAAK,KAAK;AAAA,QAC5B,WAAW,aAAa,OAAO,EAAE,KAAK,MAAM,EAAE;AAAA,QAC9C,cAAuB,oBAAAA,KAAK,iBAAiB;AAAA,UAC3C;AAAA,UACA,aAAa,eAAe,OAAO,cAAc;AAAA,UACjD;AAAA,UACA;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW1D,SAAS,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ADpIJ,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAC9B,IAAM,gBAAgB;AAAA,EACpB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT;AAYA,SAAS,SAAS,OAAO;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,SAAS,CAAC,GAAG,eAAe,WAAW;AACtD,aAAoB,oBAAAC,MAAM,0BAA0B;AAAA,IAClD,QAAQ,OAAO,IAAI,OAAK,SAAS;AAAA,MAC/B,MAAM;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,SAAS,OAAO,QAAQ,CAAC;AAAA,MAC9B,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM,CAAC,GAAG,IAAI,MAAM,KAAK,IAAI,GAAG,OAAO,IAAI,OAAK,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU,KAAK;AAAA,IAC3F,CAAC;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA,sBAAsB,WAAW,OAAO,SAAS,QAAQ,aAAa,WAAW,iBAAiB,OAAO,SAAS,cAAc,OAAO,WAAW,iBAAiB,OAAO,SAAS,cAAc,OAAO;AAAA,IACxM,UAAU,KAAc,oBAAAC,KAAK,YAAY;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,OAAgB,oBAAAA,KAAK,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,OAAgB,oBAAAA,KAAK,cAAc,SAAS,CAAC,GAAG,QAAQ;AAAA,MACvD;AAAA,MACA;AAAA,IACF,CAAC,CAAC,OAAgB,oBAAAA,KAAK,qBAAqB,SAAS,CAAC,GAAG,aAAa,CAAC,OAAgB,oBAAAA,KAAK,eAAe,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ;AAAA,EAC7I,CAAC;AACH;AACA,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,eAAe,mBAAAC,QAAU,MAAM;AAAA,IAC7B,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC3C,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC7C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC/C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,SAAS,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC7C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC5C,QAAQ,mBAAAA,QAAU;AAAA,IAClB,UAAU,mBAAAA,QAAU,MAAM;AAAA,MACxB,YAAY,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,CAAC,EAAE;AAAA,MACzD,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,UAAU,KAAK,CAAC,EAAE;AAAA,IACzD,CAAC;AAAA,IACD,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,KAAK,mBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA,EACD,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,WAAW,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC9C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACxC,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,kBAAkB,SAAS,OAAO,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IACrG,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACxE,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,MACtC,OAAO,mBAAAA,QAAU;AAAA,MACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,MAC5D,OAAO,mBAAAA,QAAU;AAAA,MACjB,OAAO,mBAAAA,QAAU,OAAO;AAAA,IAC1B,CAAC,CAAC,EAAE;AAAA,IACJ,UAAU,mBAAAA,QAAU;AAAA,IACpB,OAAO,mBAAAA,QAAU,MAAM;AAAA,MACrB,kBAAkB,mBAAAA,QAAU;AAAA,MAC5B,gBAAgB,mBAAAA,QAAU;AAAA,MAC1B,OAAO,mBAAAA,QAAU;AAAA,MACjB,cAAc,mBAAAA,QAAU;AAAA,MACxB,aAAa,mBAAAA,QAAU;AAAA,MACvB,aAAa,mBAAAA,QAAU;AAAA,MACvB,cAAc,mBAAAA,QAAU;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,mBAAAA,QAAU,MAAM;AAAA,MAC3B,kBAAkB,mBAAAA,QAAU;AAAA,MAC5B,gBAAgB,mBAAAA,QAAU;AAAA,MAC1B,OAAO,mBAAAA,QAAU;AAAA,MACjB,cAAc,mBAAAA,QAAU;AAAA,MACxB,aAAa,mBAAAA,QAAU;AAAA,MACvB,aAAa,mBAAAA,QAAU;AAAA,MACvB,cAAc,mBAAAA,QAAU;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,mBAAAA,QAAU,MAAM;AAAA,MAC9B,OAAO,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACnD,aAAa,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACzD,CAAC;AAAA,IACD,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,aAAa,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACrE,aAAa,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACrE,cAAc,mBAAAA,QAAU;AAAA,IACxB,eAAe,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,OAAO,QAAQ,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC7F,YAAY,mBAAAA,QAAU;AAAA,IACtB,MAAM,mBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAC7B,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA,EACrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,mBAAAA,QAAU;AAAA,EACjB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,SAAS,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EACnD,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM;AAAA,IAC5C,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,KAAK,CAAC;AAAA,IAC3C,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,EACtB,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACrB,SAAS,mBAAAA,QAAU,MAAM;AAAA,IACvB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,OAAO,mBAAAA,QAAU;AAAA,IACjB,GAAG,mBAAAA,QAAU;AAAA,IACb,GAAG,mBAAAA,QAAU;AAAA,EACf,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF,OAAO,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,SAAS,mBAAAA,QAAU;AAAA,IACnB,MAAM,mBAAAA,QAAU;AAAA,IAChB,SAAS,mBAAAA,QAAU;AAAA,IACnB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,IACxB,MAAM,mBAAAA,QAAU;AAAA,IAChB,aAAa,mBAAAA,QAAU;AAAA,IACvB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU;AAAA,IACjB,eAAe,mBAAAA,QAAU;AAAA,IACzB,YAAY,mBAAAA,QAAU;AAAA,IACtB,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,WAAW,IAAI,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,UAAU,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC;AAAA,IAC5D,SAAS,mBAAAA,QAAU;AAAA,IACnB,WAAW,mBAAAA,QAAU,MAAM,CAAC,QAAQ,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAC3F,WAAW,mBAAAA,QAAU;AAAA,IACrB,OAAO,mBAAAA,QAAU;AAAA,IACjB,QAAQ,mBAAAA,QAAU;AAAA,IAClB,cAAc,mBAAAA,QAAU;AAAA,IACxB,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,YAAY,mBAAAA,QAAU;AAAA,IACtB,UAAU,mBAAAA,QAAU;AAAA,IACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC;AACJ,IAAI;", "names": ["React", "import_prop_types", "React", "import_prop_types", "React", "import_prop_types", "_jsx", "PropTypes", "React", "import_jsx_runtime", "_excluded", "_excluded2", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "import_jsx_runtime", "_excluded", "useUtilityClasses", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "_excluded2", "_jsx", "PropTypes", "import_jsx_runtime", "_jsxs", "_jsx", "PropTypes", "import_jsx_runtime", "_jsxs", "_jsx", "PropTypes"]}